"""
Simply Supported Bridge Deck Module

This module handles the creation and configuration of discontinuous bridge deck elements.
"""

import openseespy.opensees as ops
import numpy as np


def create_deck(model):
    """Generate discontinuous bridge deck structure (nodes + elements)"""
    generate_deck_nodes(model)
    generate_deck_elements(model)
    
    # 模拟桥台横向挡块
    ops.fix(model.span_nodes[0]['start'], 0, 1, 0, 0, 0, 0)
    ops.fix(model.span_nodes[max(model.span_nodes.keys())]['end'], 0, 1, 0, 0, 0, 0)


def generate_deck_nodes(model):
    """生成简支梁桥不连续跨的节点
    
    为每个跨创建独立的节点序列，实现简支梁桥的结构特性
    
    Args:
        model: 桥梁模型对象
    """
    x = 0.0
    spacing = model._calculate_mesh_spacing()

    # 存储各跨的起止节点信息
    model.span_nodes = {}

    for span_idx, span_length in enumerate(model.params.span_lengths):
        span_start_node = None
        span_end_node = None

        # 计算当前跨的节点数量
        num_nodes = max(2, int(np.ceil(span_length / spacing)))
        x_coords = np.linspace(x, x + span_length, num_nodes, endpoint=True)

        # 创建当前跨的节点
        span_nodes = []
        for x_pos in x_coords:
            node_tag = model._next_tag('node')
            ops.node(node_tag, x_pos, 0.0, 0.0)
            span_nodes.append(node_tag)

            # 记录跨起止节点
            if np.isclose(x_pos, x):
                span_start_node = node_tag
            if np.isclose(x_pos, x + span_length):
                span_end_node = node_tag

        # 存储当前跨的节点信息
        model.span_nodes[span_idx] = {
            'start': span_start_node,
            'end': span_end_node,
            'all': span_nodes
        }

        # 添加到主梁节点列表
        model.deck['nodes'].extend(span_nodes)

        # 标记支承节点位置
        model.supports.append(span_start_node)
        model.supports.append(span_end_node)

        x += span_length


def generate_deck_elements(model):
    """创建主梁单元
    
    为每个跨创建弹性梁柱单元
    
    Args:
        model: 桥梁模型对象
    """
    girder_section = model.sections['Deck']

    # 逐跨创建主梁单元
    for span_idx, span_data in model.span_nodes.items():
        span_nodes = span_data['all']

        # 在当前跨内创建单元
        for i in range(len(span_nodes) - 1):
            n_i = span_nodes[i]
            n_j = span_nodes[i + 1]

            # 验证节点坐标有效性
            x_i = ops.nodeCoord(n_i, 1)
            x_j = ops.nodeCoord(n_j, 1)
            if np.isclose(x_i, x_j):
                raise ValueError(f"节点 {n_i} 和 {n_j} 坐标重复")

            # 创建弹性梁柱单元
            elem_tag = model._next_tag('element')
            ops.element('elasticBeamColumn', elem_tag,
                        n_i, n_j,
                        girder_section,
                        model.transf_tag_beam)

            # 记录单元类型
            if not hasattr(model, 'element_types'):
                model.element_types = {}
            model.element_types[elem_tag] = 'elasticBeamColumn'

            model.deck['elements'].append(elem_tag)

