import openseespy.opensees as ops
from core.model import BridgeModel
from params import BridgeParams
from materials.concrete import set_concrete
from materials.steel import set_steel
from materials.rubber import set_rubber
from materials.soil import set_backfill, set_pile_abutment, set_pile_pier
from materials.rigid_link import set_rigid_link
from sections.box_girder import set_box_girder_section
from sections.circular_pier import set_circular_pier_section
from sections.hollow_slab import set_hollow_slab_section
from sections.rectangular_cap_beam import set_rectangular_cap_beam_section

# Import component modules
from components.simply_supported_beam.deck import create_deck
from components.simply_supported_beam.pier import create_piers
from components.simply_supported_beam.isolator import create_isolators
from components.simply_supported_beam.boundary import apply_boundary_conditions
from components.simply_supported_beam.load import apply_loads


class SimplySupportedBeamModel(BridgeModel):
    """简支梁桥有限元模型
    
    特点:
    - 各跨主梁在桥墩处不连续，实现简支约束
    - 盖梁与桥墩刚性连接
    - 橡胶支座连接盖梁与主梁
    - 支持横桥向多排桥墩布置
    - 支持每个盖梁上多个支座布置
    - 兼容无桥墩的单跨桥梁
    """

    def __init__(self, params: BridgeParams):
        super().__init__(params)
        self._build_model()

    def _build_model(self):
        """构建完整的桥梁有限元模型"""
        self._init_model()
        self._create_materials()
        self._create_sections()
        self._create_components()
        self._apply_boundary_conditions()
        self._apply_loads()

    def _create_components(self):
        """创建桥梁结构组件"""
        create_deck(self)                   # 创建主梁
        create_piers(self)                  # 创建桥墩和盖梁
        create_isolators(self)              # 创建支座

    def _apply_boundary_conditions(self):
        """施加边界条件"""
        apply_boundary_conditions(self)


    def _create_materials(self):
        """创建材料本构模型"""
        # 定义材料标签映射
        self.mat_tags = {
            # 混凝土材料
            'ConcreteCover':     self._next_tag('material'),    # 保护层混凝土（非约束）
            'ConcreteCore':      self._next_tag('material'),    # 核心区混凝土（约束）
            
            # 钢筋材料
            'SteelLongitudinal': self._next_tag('material'),    # 纵向钢筋
            'SteelTransverse':   self._next_tag('material'),    # 横向箍筋
            
            # 支座材料
            'RubberX':           self._next_tag('material'),    # 橡胶支座水平剪切
            'RubberZ':           self._next_tag('material'),    # 橡胶支座竖直受压
            'RubberRxy':         self._next_tag('material'),    # 橡胶支座弯曲
            'RubberRz':          self._next_tag('material'),    # 橡胶支座转动
            
            # 土体材料
            'BackfillLeft':      self._next_tag('material'),    # 左桥台填土
            'BackfillRight':     self._next_tag('material'),    # 右桥台填土
            'PileAbutment':      self._next_tag('material'),    # 桥台桩土相互作用
            'PilePierColumn':    self._next_tag('material'),    # 桥墩桩土相互作用
            
            # 特殊材料
            'RigidLink':         self._next_tag('material'),    # 刚性连接
            'CollisionLeft':     self._next_tag('material'),    # 主梁-左桥台碰撞
            'CollisionRight':    self._next_tag('material'),    # 主梁-右桥台碰撞
            'CollisionGirder':   self._next_tag('material'),    # 主梁-主梁纵向碰撞
            'RestoringGap':      self._next_tag('material'),    # 间隙恢复弹簧
        }
        
        # 创建基本材料
        set_concrete(self.params, self.mat_tags)
        set_steel(self.params, self.mat_tags)
        set_rubber(self.params, self.mat_tags)
        set_backfill(self.params, self.mat_tags)
        set_rigid_link(self.params, self.mat_tags)
        
        # 创建土-结构相互作用材料（可选）
        if self.params.pile_soil["enable_ssi"]:
            set_pile_abutment(self.params, self.mat_tags)
            set_pile_pier(self.params, self.mat_tags)

    def _create_sections(self):
        """创建结构截面"""
        # 创建主梁截面
        self._create_girder_section()
        
        # 创建桥墩截面（多跨桥梁）
        if self.params.num_spans > 1:
            self._create_pier_section()
            self._create_cap_beam_section()

    def _create_girder_section(self):
        """创建主梁截面"""
        girder_type = self.params.girder
        section_tag = len(self.sections) + 1
        
        if girder_type == 'hollow_slab':
            self.sections['Deck'] = set_hollow_slab_section(section_tag, self.params)
        elif girder_type == 'box':
            self.sections['Deck'] = set_box_girder_section(section_tag, self.params)
        else:
            raise ValueError(f"不支持的主梁截面类型: {girder_type}")

    def _create_pier_section(self):
        """创建桥墩截面"""
        section_tag = len(self.sections) + 1
        self.sections['Pier'] = set_circular_pier_section(
            section_tag, self.params, self.mat_tags
        )

    def _create_cap_beam_section(self):
        """创建盖梁截面"""
        section_tag = len(self.sections) + 1
        self.sections['Cap'] = set_rectangular_cap_beam_section(
            section_tag, self.params, self.mat_tags
        )


    def _apply_loads(self):
        """施加荷载"""
        apply_loads(self)