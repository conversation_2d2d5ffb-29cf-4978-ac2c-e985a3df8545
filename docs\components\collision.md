# Collision对象技术文档

## 概述

Collision对象负责桥梁结构分析模型中碰撞单元的创建和管理，用于模拟桥梁结构在地震作用下可能发生的碰撞效应。该对象主要处理两类碰撞：主梁与桥台之间的碰撞以及相邻主梁跨间的碰撞，通过零长度单元和间隙材料实现碰撞的非线性行为。

## 数据结构

### 基本结构
```python
model.collision = {
    'elements': []      # 碰撞单元标签列表
}
```

### 碰撞类型组织

#### 桥台碰撞单元
- **左桥台碰撞**: 主梁左端与左桥台的碰撞
- **右桥台碰撞**: 主梁右端与右桥台的碰撞
- **恢复弹簧**: 防止数值漂移的小刚度弹簧

#### 跨间碰撞单元
- **正向碰撞**: 后跨推前跨的碰撞
- **负向碰撞**: 前跨推后跨的碰撞
- **恢复弹簧**: 维持数值稳定性

## 关键参数

### 碰撞间隙参数
```python
abutment = {
    "gap": 0.05,                    # 桥台初始间隙 (m)
}

# 跨间碰撞间隙（自动计算）
pier_gap = max(0.02, min(0.04, abutment_gap/2))  # m
```

### 碰撞刚度参数
```python
# 碰撞刚度基于主梁轴向刚度
axial_stiffness = E * A / L * 0.3   # 30%的主梁轴向刚度

# 恢复弹簧刚度
restoring_stiffness = axial_stiffness / 1000
```

## 材料模型

### 桥台碰撞材料

#### 左桥台碰撞材料
```python
ops.uniaxialMaterial('ElasticPPGap', collision_mat_left,
    axial_stiffness,    # 弹性刚度 (N/m)
    1e10,               # 正向屈服强度 (N) - 很大值
    gap,                # 正向间隙 (m)
    0                   # 负向屈服强度 (N) - 无负向约束
)
```

#### 右桥台碰撞材料
```python
ops.uniaxialMaterial('ElasticPPGap', collision_mat_right,
    axial_stiffness,    # 弹性刚度 (N/m)
    -1e10,              # 正向屈服强度 (N) - 无正向约束
    -gap,               # 负向间隙 (m)
    0                   # 负向屈服强度 (N) - 很大值
)
```

### 跨间碰撞材料
```python
ops.uniaxialMaterial('ElasticPPGap', collision_mat_girder,
    axial_stiffness,    # 弹性刚度 (N/m)
    1e10,               # 正向屈服强度 (N)
    gap/2,              # 间隙 (m)
    0                   # 负向屈服强度 (N)
)
```

### 恢复弹簧材料
```python
ops.uniaxialMaterial('Elastic', restoring_mat,
    restoring_stiffness  # 小刚度防止数值漂移
)
```

## 碰撞刚度计算

### 主梁轴向刚度计算
```python
def calculate_beam_axial_stiffness(model):
    """计算主梁轴向刚度"""
    girder_type = model.params.girder
    section_params = model.params.girder_section[girder_type]
    
    # 混凝土弹性模量
    E = model.params.concrete_materials['core']['Ec'] * 1e6  # Pa
    
    # 截面面积计算
    if girder_type == 'box':
        # 箱梁截面面积
        width = model.params.deck_width
        height = section_params['height']
        top_thick = section_params['top_slab_thickness']
        bottom_thick = section_params['bottom_slab_thickness']
        web_thick = section_params['web_thickness']
        
        # 净截面面积
        area = (width * top_thick + width * bottom_thick + 
                2 * web_thick * (height - top_thick - bottom_thick))
    
    elif girder_type == 'hollow_slab':
        # 空心板截面面积
        slab_width = section_params['slab_width']
        slab_height = section_params['height']
        hollow_width = section_params['hollow_width']
        hollow_height = section_params['hollow_height']
        
        # 单板净面积
        single_area = slab_width * slab_height - hollow_width * hollow_height
        # 总面积
        num_slabs = model.params.deck_width // slab_width
        area = single_area * num_slabs
    
    # 轴向刚度 (EA/L)
    min_span = min(model.params.span_lengths)
    axial_stiffness = E * area / min_span
    
    # 折减系数0.3
    return axial_stiffness * 0.3
```

## 碰撞单元创建

### 桥台碰撞单元
```python
def add_abutment_collision(model, abutment_node, collision_mat, restoring_mat, side):
    """创建桥台碰撞单元"""
    # 确定主梁端部节点
    if side == 'left':
        deck_node = model.span_nodes[0]['start']
    else:  # side == 'right'
        deck_node = model.span_nodes[max(model.span_nodes.keys())]['end']
    
    # 创建碰撞单元
    elem_tag = model._next_tag('element')
    ops.element('zeroLength', elem_tag,
                deck_node, abutment_node,
                '-mat', collision_mat,
                '-dir', 1)  # X方向
    model.collision['elements'].append(elem_tag)
    
    # 创建恢复弹簧
    restoring_elem = model._next_tag('element')
    ops.element('zeroLength', restoring_elem,
                deck_node, abutment_node,
                '-mat', restoring_mat,
                '-dir', 1)
    model.collision['elements'].append(restoring_elem)
```

### 跨间碰撞单元
```python
def add_pier_collision_elements(model, axial_stiffness, gap, restoring_mat):
    """创建跨间碰撞单元"""
    # 遍历每个桥墩位置
    for pier_idx in model.cap_beams['nodes'].keys():
        span_before = pier_idx
        span_after = pier_idx + 1
        
        # 检查相邻跨是否存在
        if (span_before not in model.span_nodes or 
            span_after not in model.span_nodes):
            continue
        
        # 获取相邻跨的端部节点
        end_node_before = model.span_nodes[span_before]['end']
        start_node_after = model.span_nodes[span_after]['start']
        
        # 正向碰撞单元（后跨推前跨）
        elem_pos = model._next_tag('element')
        ops.element('zeroLength', elem_pos,
                    start_node_after, end_node_before,
                    '-mat', collision_mat,
                    '-dir', 1)
        model.collision['elements'].append(elem_pos)
        
        # 负向碰撞单元（前跨推后跨）
        elem_neg = model._next_tag('element')
        ops.element('zeroLength', elem_neg,
                    end_node_before, start_node_after,
                    '-mat', collision_mat,
                    '-dir', 1)
        model.collision['elements'].append(elem_neg)
        
        # 恢复弹簧
        restoring_elem = model._next_tag('element')
        ops.element('zeroLength', restoring_elem,
                    start_node_after, end_node_before,
                    '-mat', restoring_mat,
                    '-dir', 1)
        model.collision['elements'].append(restoring_elem)
```

## 与其他组件的关系

### 主梁连接
- 碰撞单元连接主梁端部节点
- 不影响主梁内部的结构行为
- 仅在发生碰撞时激活

### 桥台连接
- 连接主梁端部与桥台节点
- 考虑初始间隙和接触刚度
- 模拟桥台挡块的约束效应

### 支座相互作用
- 碰撞可能影响支座的受力状态
- 碰撞力通过支座传递到下部结构
- 需要考虑碰撞与支座失效的耦合

## 分析功能

### 碰撞检测
```python
def detect_collision(model):
    """检测碰撞发生"""
    collision_status = {}
    
    for elem_tag in model.collision['elements']:
        # 获取单元变形
        deformation = ops.eleResponse(elem_tag, 'deformation')
        
        # 判断是否发生碰撞
        if abs(deformation[0]) > 1e-6:  # 阈值
            collision_status[elem_tag] = {
                'active': True,
                'deformation': deformation[0],
                'force': ops.eleResponse(elem_tag, 'force')[0]
            }
        else:
            collision_status[elem_tag] = {'active': False}
    
    return collision_status
```

### 碰撞力记录
```python
def record_collision_forces(model):
    """记录碰撞力时程"""
    # 选择代表性碰撞单元进行记录
    representative_elements = model.collision['elements'][::2]  # 每隔一个
    
    ops.recorder('Element', '-file', 'collision_forces.out',
                 '-ele', *representative_elements,
                 'force')
```

### 碰撞能量耗散
```python
def calculate_collision_energy(model, time_history):
    """计算碰撞耗散能量"""
    total_energy = 0
    
    for elem_tag in model.collision['elements']:
        # 获取力-位移时程
        force_history = []  # 需要从记录文件读取
        disp_history = []   # 需要从记录文件读取
        
        # 计算滞回能量
        energy = 0
        for i in range(1, len(force_history)):
            energy += 0.5 * (force_history[i] + force_history[i-1]) * \
                     (disp_history[i] - disp_history[i-1])
        
        total_energy += abs(energy)
    
    return total_energy
```

## 使用示例

### 创建碰撞单元
```python
# 添加碰撞单元
add_collision_elements(model)

# 检查碰撞单元数量
print(f"创建了 {len(model.collision['elements'])} 个碰撞单元")
```

### 碰撞分析
```python
# 地震分析中监测碰撞
def earthquake_analysis_with_collision(model):
    # 进行时程分析
    for step in range(num_steps):
        ops.analyze(1, dt)
        
        # 检测碰撞
        collision_status = detect_collision(model)
        
        # 记录碰撞信息
        active_collisions = sum(1 for status in collision_status.values() 
                               if status.get('active', False))
        
        if active_collisions > 0:
            print(f"时刻 {step*dt:.3f}s: {active_collisions} 个碰撞单元激活")
```

### 碰撞参数敏感性分析
```python
def collision_sensitivity_analysis(model, gap_range):
    """碰撞间隙参数敏感性分析"""
    results = {}
    
    for gap in gap_range:
        # 更新碰撞间隙
        model.params.abutment['gap'] = gap
        
        # 重新创建碰撞单元
        recreate_collision_elements(model)
        
        # 进行分析
        max_response = run_earthquake_analysis(model)
        results[gap] = max_response
    
    return results
```

## 建模策略

### 碰撞建模原则
1. **合理间隙**: 基于实际构造确定初始间隙
2. **适当刚度**: 碰撞刚度不宜过大或过小
3. **数值稳定**: 使用恢复弹簧防止数值问题

### 参数选择指导
1. **间隙大小**: 通常为2-5cm，基于伸缩缝宽度
2. **碰撞刚度**: 主梁轴向刚度的10%-50%
3. **恢复刚度**: 碰撞刚度的0.1%

### 分析注意事项
1. **收敛性**: 碰撞可能导致收敛困难
2. **时间步长**: 需要足够小的时间步长
3. **阻尼设置**: 合理设置阻尼避免高频振动

## 注意事项

1. **单元方向**: 确保碰撞单元方向正确
2. **材料参数**: 间隙材料参数设置要合理
3. **数值稳定**: 恢复弹簧防止数值漂移
4. **收敛控制**: 碰撞分析需要调整收敛参数
5. **结果解释**: 碰撞力可能出现数值振荡
6. **参数验证**: 通过简单算例验证碰撞模型

## 相关模块

- `components/collision.py`: 碰撞单元实现
- `materials/gap.py`: 间隙材料定义
- `analysis/nonlinear.py`: 非线性分析模块
- `analysis/recorder/collision.py`: 碰撞响应记录
- `analysis/check_accidents/collision.py`: 碰撞事故检查