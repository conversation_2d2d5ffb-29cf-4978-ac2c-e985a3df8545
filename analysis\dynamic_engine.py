"""
动力分析引擎模块

提供非线性动力时程分析的核心时间步进逻辑
支持分析步长和输出步长分离
"""

import openseespy.opensees as ops


class DynamicAnalysisEngine:
    """动力分析引擎
    
    负责执行非线性动力时程分析的时间步进循环
    支持自适应时间步长和备选算法策略
    """
    
    def __init__(self, dt_analysis=0.01, dt_output=0.01):
        """初始化动力分析引擎

        Args:
            dt_analysis (float): 分析时间步长 (s)
            dt_output (float): 输出时间步长 (s)
        """
        self.dt_analysis = dt_analysis
        self.dt_output = dt_output
        self.analysis_stats = {}
        
    def run_analysis(self, target_time, record_callback, try_algorithms_callback=None):
        """执行动力时程分析
        
        Args:
            target_time (float): 目标分析时间 (s)
            record_callback (callable): 响应记录回调函数，接收当前时间作为参数
            try_algorithms_callback (callable, optional): 备选算法尝试回调函数
            
        Returns:
            dict: 分析统计信息
        """
        # 初始化分析统计
        self.analysis_stats = {
            'total_steps': 0,
            'successful_steps': 0,
            'algorithm_switches': 0,
            'skipped_steps': 0
        }
        
        current_time = 0.0
        dt_fixed = self.dt_analysis
        
        print(f"\n开始地震动力分析, 目标时间 {target_time:.2f}s, 时间步长 {dt_fixed:.3f}s...")
        
        step = 0
        while current_time < target_time - 1e-10:
            step += 1
            success = False
            
            # 尝试标准算法分析当前时间步
            try:
                if ops.analyze(1, dt_fixed) == 0:
                    success = True
            except Exception:
                pass
            
            # 标准算法失败时，尝试备选算法
            if not success and try_algorithms_callback:
                print(f"\n时间步 {step} (t={current_time:.3f}s) 标准算法失败，尝试备选算法...")
                success = try_algorithms_callback(dt_fixed)
                if success:
                    self.analysis_stats['algorithm_switches'] += 1
            
            # 所有算法均失败时，跳过当前时间步
            if not success:
                print(f"\n警告: 时间 {current_time:.3f}s 无法收敛，跳过当前步")
                current_time += dt_fixed
                self.analysis_stats['skipped_steps'] += 1
                continue
            
            # 成功分析当前时间步
            if success:
                ops.reactions()
                current_time += dt_fixed
                self.analysis_stats['successful_steps'] += 1
                
                # 按输出步长记录响应
                if self._should_record(current_time):
                    record_callback(current_time)
            
            self.analysis_stats['total_steps'] += 1
            
            # 显示分析进度
            if (self.analysis_stats['total_steps'] % 10 == 0) or (abs(current_time-target_time) < 1e-10):
                progress = current_time / target_time * 100
                print(f"\r地震分析进度: {current_time:.3f}/{target_time:.3f}s ({progress:.1f}%)", end="")
        
        print(f"\n地震动力分析完成 {current_time:.3f}/{target_time:.3f}s")
        self._print_stats()
        
        return self.analysis_stats
    
    def _should_record(self, current_time):
        """判断当前时间是否应该记录响应

        Args:
            current_time (float): 当前时间

        Returns:
            bool: 是否应该记录
        """
        # 如果分析步长等于输出步长，每步都记录
        if abs(self.dt_analysis - self.dt_output) < 1e-12:
            return True

        # 计算当前时间对应的输出步数
        output_step = round(current_time / self.dt_output)
        expected_time = output_step * self.dt_output

        # 如果当前时间接近输出时间点，则记录
        return abs(current_time - expected_time) < 1e-9
    
    def _print_stats(self):
        """打印分析统计信息"""
        print(f"总步数: {self.analysis_stats['total_steps']}")
        print(f"成功步数: {self.analysis_stats['successful_steps']}")
        print(f"算法切换次数: {self.analysis_stats['algorithm_switches']}")
        print(f"跳过步数: {self.analysis_stats['skipped_steps']}")


class AdaptiveDynamicAnalysisEngine(DynamicAnalysisEngine):
    """自适应动力分析引擎
    
    支持自适应时间步长调整，提高收敛性能
    """
    
    def __init__(self, dt_analysis=0.01, dt_output=0.02, dt_min=1e-5):
        """初始化自适应动力分析引擎
        
        Args:
            dt_analysis (float): 初始分析时间步长 (s)
            dt_output (float): 输出时间步长 (s)
            dt_min (float): 最小时间步长 (s)
        """
        super().__init__(dt_analysis, dt_output)
        self.dt_min = dt_min
        
    def run_analysis(self, target_time, record_callback, try_algorithms_callback=None):
        """执行自适应动力时程分析
        
        Args:
            target_time (float): 目标分析时间 (s)
            record_callback (callable): 响应记录回调函数
            try_algorithms_callback (callable, optional): 备选算法尝试回调函数
            
        Returns:
            dict: 分析统计信息
        """
        # 初始化分析统计
        self.analysis_stats = {
            'total_steps': 0,
            'successful_steps': 0,
            'successful_consecutive': 0,
            'algorithm_switches': 0,
            'skipped_steps': 0,
            'adaptive_steps': 0,
            'min_dt_used': self.dt_analysis
        }
        
        current_time = 0.0
        dt_current = self.dt_analysis
        next_output_time = self.dt_output
        
        print(f"\n开始自适应地震动力分析, 目标时间 {target_time:.2f}s...")
        print(f"分析步长: {self.dt_analysis:.4f}s, 输出步长: {self.dt_output:.4f}s")
        
        step = 0
        while current_time < target_time - 1e-10:
            step += 1
            
            # 调整时间步以精确到达输出时间点
            dt_step, dt_backup = self._adjust_timestep_for_output(
                current_time, dt_current, next_output_time)
            
            # 尝试分析当前步
            success = self._try_analyze_step(dt_step, try_algorithms_callback)
            
            # 如果失败，自适应减小时间步
            if not success:
                success, dt_step = self._adaptive_reduce_timestep(
                    step, current_time, dt_step, try_algorithms_callback)
            
            # 仍然失败则抛出异常
            if not success:
                raise RuntimeError(f"分析在时间 {current_time:.3f}s 未收敛")
            
            # 成功分析当前步
            ops.reactions()
            current_time += dt_step
            self.analysis_stats['successful_steps'] += 1
            self.analysis_stats['successful_consecutive'] += 1
            
            # 记录响应（精确到达输出时间点）
            if abs(current_time - next_output_time) < 1e-9:
                record_callback(current_time)
                next_output_time += self.dt_output
            
            # 恢复时间步
            if dt_backup is not None:
                dt_current = dt_backup
            
            # 时间步恢复策略
            dt_current = self._recover_timestep(dt_current)
            
            self.analysis_stats['total_steps'] += 1
            
            # 显示进度
            if (self.analysis_stats['total_steps'] % 20 == 0) or (abs(current_time-target_time) < 1e-8):
                progress = current_time / target_time * 100
                print(f"\r分析进度: {progress:.1f}% ({current_time:.3f}s/{target_time:.3f}s, dt={dt_current:.6f}s)", end="")
        
        print(f"\n分析完成: {self.analysis_stats['successful_steps']}/{self.analysis_stats['total_steps']} 步成功")
        self._print_adaptive_stats()
        
        return self.analysis_stats
    
    def _adjust_timestep_for_output(self, current_time, dt_current, next_output_time):
        """调整时间步以精确到达输出时间点
        
        Args:
            current_time (float): 当前时间
            dt_current (float): 当前时间步
            next_output_time (float): 下一个输出时间点
            
        Returns:
            tuple: (调整后的时间步, 备份的时间步或None)
        """
        remaining = next_output_time - current_time
        if remaining > 0 and dt_current > remaining:
            return remaining, dt_current
        return dt_current, None
    
    def _try_analyze_step(self, dt_step, try_algorithms_callback):
        """尝试分析当前步
        
        Args:
            dt_step (float): 时间步长
            try_algorithms_callback (callable): 备选算法回调
            
        Returns:
            bool: 是否成功
        """
        # 尝试标准算法
        if ops.analyze(1, dt_step) == 0:
            return True
        
        # 尝试备选算法
        if try_algorithms_callback:
            if try_algorithms_callback(dt_step):
                self.analysis_stats['algorithm_switches'] += 1
                return True
        
        return False
    
    def _adaptive_reduce_timestep(self, step, current_time, dt_step, try_algorithms_callback):
        """自适应减小时间步
        
        Args:
            step (int): 当前步数
            current_time (float): 当前时间
            dt_step (float): 当前时间步
            try_algorithms_callback (callable): 备选算法回调
            
        Returns:
            tuple: (是否成功, 最终时间步)
        """
        while dt_step > self.dt_min:
            dt_step *= 0.5
            self.analysis_stats['adaptive_steps'] += 1
            print(f"\n时间步 {step} (t={current_time:.3f}s) 未收敛，减小时间步为 {dt_step:.6f}s...")
            
            self.analysis_stats['successful_consecutive'] = 0
            if dt_step < self.analysis_stats['min_dt_used']:
                self.analysis_stats['min_dt_used'] = dt_step
            
            # 尝试分析
            if self._try_analyze_step(dt_step, try_algorithms_callback):
                return True, dt_step
        
        return False, dt_step
    
    def _recover_timestep(self, dt_current):
        """时间步恢复策略
        
        Args:
            dt_current (float): 当前时间步
            
        Returns:
            float: 恢复后的时间步
        """
        if dt_current < self.dt_analysis and self.analysis_stats['successful_consecutive'] >= 5:
            dt_current = min(dt_current * 2.0, self.dt_analysis)
            self.analysis_stats['successful_consecutive'] = 0
        return dt_current
    
    def _print_adaptive_stats(self):
        """打印自适应分析统计信息"""
        self._print_stats()
        if self.analysis_stats['adaptive_steps'] > 0:
            print(f"动态时间步调整次数: {self.analysis_stats['adaptive_steps']}")
            print(f"使用的最小时间步: {self.analysis_stats['min_dt_used']:.6f}s")

