"""
支座响应绘图工具

该模块提供绘制支座地震响应曲线的功能：
1. 剪力-相对位移曲线
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from typing import Optional, Dict


def read_bearing_response_data(results_dir: str, bearing_id: str) -> Optional[Dict]:
    """
    读取支座响应数据
    
    参数:
        results_dir: 结果文件目录
        bearing_id: 支座ID，格式如 "bearing_38_x10.0_y-0.2"
        
    返回:
        包含时间、剪力、相对位移数据的字典，如果读取失败返回None
    """
    bearing_data = {}
    
    # 构建文件路径
    force_file = os.path.join(results_dir, f'bearing_force_{bearing_id}.txt')
    deform_file = os.path.join(results_dir, f'bearing_deform_{bearing_id}.txt')
    
    # 读取剪力数据
    if os.path.exists(force_file):
        try:
            force_data = np.loadtxt(force_file)
            if force_data.size > 0:
                if force_data.ndim == 1:
                    force_data = force_data.reshape(1, -1)
                bearing_data['time'] = force_data[:, 0]
                # 支座力格式: time, Fx1, Fy1, Fz1, Mx1, My1, Mz1, Fx2, Fy2, Fz2, Mx2, My2, Mz2
                # 取第一端的剪力
                bearing_data['shear_x'] = force_data[:, 1]  # X方向剪力 (N)
                bearing_data['shear_y'] = force_data[:, 2]  # Y方向剪力 (N)
                bearing_data['axial'] = force_data[:, 3]    # 轴向力 (N)
        except Exception as e:
            print(f"读取剪力文件 {force_file} 时出错: {e}")
            return None
    else:
        print(f"剪力文件不存在: {force_file}")
        return None
            
    # 读取相对位移数据
    if os.path.exists(deform_file):
        try:
            deform_data = np.loadtxt(deform_file)
            if deform_data.size > 0:
                if deform_data.ndim == 1:
                    deform_data = deform_data.reshape(1, -1)
                # 支座变形格式: time, dx, dy, dz, rx, ry, rz
                bearing_data['disp_x'] = deform_data[:, 1]  # X方向相对位移 (m)
                bearing_data['disp_y'] = deform_data[:, 2]  # Y方向相对位移 (m)
                bearing_data['disp_z'] = deform_data[:, 3]  # Z方向相对位移 (m)
        except Exception as e:
            print(f"读取变形文件 {deform_file} 时出错: {e}")
            return None
    else:
        print(f"变形文件不存在: {deform_file}")
        return None
    
    if len(bearing_data) == 0:
        return None
        
    return bearing_data


def plot_shear_displacement(bearing_data: Dict, direction: str = 'x', output_file: Optional[str] = None):
    """
    绘制剪力-相对位移曲线
    
    参数:
        bearing_data: 支座响应数据字典
        direction: 方向 ('x' 或 'y')
        output_file: 输出文件路径，如果为None则不保存
    """
    direction = direction.lower()
    
    # 检查数据是否存在
    shear_key = f'shear_{direction}'
    disp_key = f'disp_{direction}'
    
    if shear_key not in bearing_data or disp_key not in bearing_data:
        print(f"缺少{direction.upper()}方向的剪力或位移数据，无法绘制")
        return
    
    # 获取数据并转换单位
    displacement = bearing_data[disp_key] * 1000  # m -> mm
    shear = bearing_data[shear_key] / 1000  # N -> kN
    
    # 创建图形
    plt.figure(figsize=(6, 5), dpi=300, constrained_layout=True)
    plt.plot(-displacement, shear, '-', linewidth=0.75, alpha=0.9)
    
    # 设置标签和标题
    plt.xlabel(f'相对位移 (mm)', fontsize=16)
    plt.ylabel(f'剪力 (kN)', fontsize=16)
    plt.title(f'支座{direction.upper()}方向剪力-相对位移曲线', fontsize=16, fontweight='bold')
    plt.grid(True, alpha=0.3)
    # plt.tight_layout()
    
    # 保存图形
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"剪力-相对位移曲线已保存至: {output_file}")
    
    plt.close()


def plot_bearing_responses(results_dir: str, bearing_id: Optional[str] = None):
    """
    绘制支座响应曲线（剪力-相对位移）
    
    参数:
        results_dir: 结果文件目录
        bearing_id: 支座ID, 如果为None则自动查找中跨的第一个支座
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimSun']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['xtick.labelsize'] = 14    # x轴刻度标签字号
    plt.rcParams['ytick.labelsize'] = 14    # y轴刻度标签字号
    
    # 如果未指定bearing_id, 查找中跨的支座
    if bearing_id is None:
        bearing_files = [f for f in os.listdir(results_dir) if f.startswith('bearing_force_')]
        if not bearing_files:
            print(f"在 {results_dir} 中未找到支座数据文件")
            return
        
        # 查找中跨支座（x坐标在10-32之间, 排除边界）
        mid_span_bearings = []
        for f in bearing_files:
            # 从文件名提取支座信息
            # 格式: bearing_force_bearing_38_x10.0_y-0.2.txt
            parts = f.replace('bearing_force_', '').replace('.txt', '').split('_')
            try:
                x_str = [p for p in parts if p.startswith('x')][0]
                x_coord = float(x_str[1:])
                # 选择中跨支座（x坐标大于5且小于37，避免边界）
                if 5 < x_coord < 37:
                    bearing_id_extracted = f.replace('bearing_force_', '').replace('.txt', '')
                    mid_span_bearings.append((bearing_id_extracted, x_coord))
            except:
                continue
        
        if not mid_span_bearings:
            print(f"在 {results_dir} 中未找到中跨支座数据")
            return
        
        # 选择第一个中跨支座
        mid_span_bearings.sort(key=lambda x: x[1])
        bearing_id = mid_span_bearings[0][0]
        print(f"自动选择中跨支座: {bearing_id}")
    
    # 读取支座数据
    bearing_data = read_bearing_response_data(results_dir, bearing_id)
    if bearing_data is None:
        print(f"无法读取支座 {bearing_id} 的数据")
        return
    
    print(f"成功读取支座 {bearing_id} 数据")
    
    # 绘制X方向剪力-相对位移曲线
    if 'shear_x' in bearing_data and 'disp_x' in bearing_data:
        output_file = os.path.join(results_dir, f'{bearing_id}_shear_displacement_x.png')
        plot_shear_displacement(bearing_data, direction='x', output_file=output_file)
    
    # 绘制Y方向剪力-相对位移曲线
    if 'shear_y' in bearing_data and 'disp_y' in bearing_data:
        output_file = os.path.join(results_dir, f'{bearing_id}_shear_displacement_y.png')
        plot_shear_displacement(bearing_data, direction='y', output_file=output_file)
    
    print(f"支座响应曲线绘制完成")


if __name__ == "__main__":
    # 测试代码
    import sys
    
    if len(sys.argv) > 1:
        results_dir = sys.argv[1]
        bearing_id = sys.argv[2] if len(sys.argv) > 2 else None
        plot_bearing_responses(results_dir, bearing_id)
    else:
        print("用法: python utils/plot_bearing_response.py <results_dir> [bearing_id]")

