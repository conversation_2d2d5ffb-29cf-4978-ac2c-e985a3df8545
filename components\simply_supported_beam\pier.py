"""
Simply Supported Bridge Pier Module

This module handles the creation and configuration of bridge pier elements with cap beams.
"""

import openseespy.opensees as ops
import numpy as np


def create_piers(model):
    """Generate pier structure with cap beams (nodes + elements)"""
    if not model.params.pier_heights or all(h == 0 for h in model.params.pier_heights):
        # No piers case - just return
        return

    generate_pier_nodes(model)
    generate_pier_elements(model)
    generate_cap_beams(model)


def generate_pier_nodes(model):
    """Create pier nodes for multiple piers in the transverse direction"""
    for pier_long_idx, height in enumerate(model.params.pier_heights):
        if height <= 0:
            continue  # Skip piers with zero height

        # Get position from the span nodes
        span_idx = pier_long_idx + 1  # Pier idx is between spans, so pier 0 is between spans 0 and 1
        if span_idx >= len(model.span_nodes):
            continue  # Skip if no corresponding span

        # Get x-coordinate from the end of the previous span (or start of next span)
        prev_span_end = model.span_nodes[span_idx-1]['end']
        x_coord = ops.nodeCoord(prev_span_end, 1)

        # Calculate transverse positions for multiple piers
        num_piers_trans = model.params.num_piers_transverse
        pier_spacing = model.params.pier_spacing_transverse

        # Calculate y-coordinates for piers in transverse direction
        if num_piers_trans == 1:
            y_coords = [0.0]  # Single pier at center
        else:
            # Multiple piers with equal spacing
            total_width = pier_spacing * (num_piers_trans - 1)
            y_coords = np.linspace(-total_width/2, total_width/2, num_piers_trans)

        # First, create all pier nodes for this longitudinal position
        pier_top_nodes = {}  # Store top nodes of each pier for later cap beam connection

        # Create piers in the transverse direction
        for pier_trans_idx, y_coord in enumerate(y_coords):
            # Create pier nodes
            # num_elements = model.params.pier_segments
            num_elements = int(np.ceil(height))
            node_spacing = height / num_elements

            # Store nodes for this pier
            pier_nodes = []

            # Create nodes from bottom to top
            for i in range(num_elements + 1):
                z_coord = -height + i * node_spacing
                node_tag = model._next_tag('node')
                ops.node(node_tag, x_coord, y_coord, z_coord)
                pier_nodes.append(node_tag)

            # Store pier nodes with composite key (longitudinal_idx, transverse_idx)
            pier_key = (pier_long_idx, pier_trans_idx)
            model.piers['nodes'][pier_key] = pier_nodes

            # Store the top node of this pier
            pier_top_nodes[pier_trans_idx] = pier_nodes[-1]

        # ----------------------------------------------------------
        # Now create a single cap beam that spans across all piers at this longitudinal position

        # Calculate bearing positions based on bearing spacing parameter
        # This ensures cap beam nodes will align with bearing positions
        bearing_spacing = model.params.bearing_spacing
        num_bearings = int(model.params.deck_width // bearing_spacing)

        # Calculate bearing positions (symmetric about center)
        total_bearing_width = bearing_spacing * (num_bearings - 1)
        bearing_y_coords = np.linspace(-total_bearing_width/2, total_bearing_width/2, num_bearings)

        # Create a single list of cap beam nodes for this longitudinal position
        cap_beam_nodes = []

        # First, add nodes at pier top positions
        for pier_trans_idx in pier_top_nodes:
            cap_beam_nodes.append(pier_top_nodes[pier_trans_idx])

        # Then add nodes at bearing positions (if not already added at pier tops)
        for bearing_y in bearing_y_coords:
            # Check if this position is close to any existing cap beam node (pier top)
            is_existing_node = False
            for node in cap_beam_nodes:
                node_y = ops.nodeCoord(node, 2)
                if abs(node_y - bearing_y) < 1e-6:  # Close enough to be considered the same position
                    is_existing_node = True
                    break

            if not is_existing_node:
                # Create a new cap beam node at the bearing position
                node_tag = model._next_tag('node')
                ops.node(node_tag, x_coord, bearing_y, 0.0)
                cap_beam_nodes.append(node_tag)

        # Sort cap beam nodes by y-coordinate for proper element creation
        cap_beam_nodes.sort(key=lambda node: ops.nodeCoord(node, 2))

        # Store cap beam nodes with the longitudinal index as key
        model.cap_beams['nodes'][pier_long_idx] = cap_beam_nodes


def generate_pier_elements(model):
    """Create nonlinear pier elements for multiple piers in the transverse direction"""
    if not model.piers['nodes']:
        return  # No piers to create

    section_tag = model.sections['Pier']
    integration_tag = 1
    
    # 使用Legendre积分方案，通常比Lobatto更稳定
    ops.beamIntegration('Legendre', integration_tag, section_tag, 5)
    # ops.beamIntegration('Lobatto', integration_tag, section_tag, 5)

    for pier_key, nodes in model.piers['nodes'].items():
        # Number of elements per pier = number of nodes - 1
        num_elements = len(nodes) - 1
        for i in range(num_elements):
            elem_tag = model._next_tag('element')
            n_i = nodes[i]
            n_j = nodes[i+1]

            # Check element length
            L = np.linalg.norm(np.array(ops.nodeCoord(n_j)) - np.array(ops.nodeCoord(n_i)))
            if L < 1e-5:
                raise ValueError(f"Pier element {n_i}-{n_j} has abnormal length: {L}m")
            
            ops.element('forceBeamColumn', elem_tag,
                    n_i, n_j, model.transf_tag_pier, integration_tag)

            # Store element type for reference
            if not hasattr(model, 'element_types'):
                model.element_types = {}
            model.element_types[elem_tag] = 'forceBeamColumn'

            model.piers['elements'].append(elem_tag)


def generate_cap_beams(model):
    """Create cap beam elements connecting to the pier tops"""
    if not model.cap_beams['nodes']:
        # print("Warning: No cap beams to create")
        return

    # Use cap beam section if defined, otherwise use deck section
    section_tag = model.sections['Cap']

    for pier_long_idx, nodes in model.cap_beams['nodes'].items():
        # Create elements connecting adjacent nodes
        for i in range(len(nodes)-1):
            elem_tag = model._next_tag('element')
            
            # Use standard elastic beam column
            ops.element('elasticBeamColumn', elem_tag,
                        nodes[i], nodes[i+1],
                        section_tag,
                        model.transf_tag_cap)

            # Store element type for reference
            if not hasattr(model, 'element_types'):
                model.element_types = {}
            model.element_types[elem_tag] = 'elasticBeamColumn'

            model.cap_beams['elements'].append(elem_tag)


