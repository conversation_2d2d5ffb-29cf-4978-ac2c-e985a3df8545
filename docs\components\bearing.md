# Bearing对象技术文档

## 概述

Bearing对象是桥梁结构分析模型中的关键组件，用于表示板式橡胶支座及其连接关系。该对象负责连接主梁与下部结构（桥墩盖梁或桥台），传递荷载并允许结构在地震作用下的相对位移。

## 数据结构

### 基本结构
```python
model.bearings = {
    'spans': [],        # 支座所属跨号列表
    'elements': [],     # 支座单元标签列表  
    'connections': []   # 支座连接关系列表
}
```

### 连接组织方式

#### 连接存储结构
```python
model.bearings['connections'] = [
    (deck_node, support_node),  # 支座连接节点对
    ...
]
```

**连接说明：**
- `deck_node`: 主梁侧连接节点（上端）
- `support_node`: 支承侧连接节点（下端，盖梁或桥台）
- 每个支座通过zeroLength单元连接两个节点

#### 空间分布规律

##### 纵桥向位置
- **桥墩处**: 位于相邻跨的连接位置，与盖梁节点对应
- **桥台处**: 位于桥梁两端，连接主梁端部与桥台

##### 横桥向分布
- 对于空心板简支梁桥，假定每块板宽1m，每端受2个支座支撑，故支座间距取0.5m
```python
# 支座横桥向布置参数
bearing_spacing = 0.5  # 支座间距 (m)
num_bearings = deck_width // bearing_spacing  # 支座数量
total_width = bearing_spacing * (num_bearings - 1)
y_coords = np.linspace(-total_width/2, total_width/2, num_bearings)
```

### 单元组织方式

#### 单元类型
- **主要单元**: `zeroLength` - 零长度连接单元
- **自由度**: 6个DOF（3个平动 + 3个转动）
- **材料分配**: 每个DOF对应不同的材料模型

#### 材料模型配置
```python
ops.element('zeroLength', elem_tag, deck_node, support_node,
    '-mat',
    mat_tags['RubberX'],    # DOF 1 (X方向平动)
    mat_tags['RubberX'],    # DOF 2 (Y方向平动) 
    mat_tags['RubberZ'],    # DOF 3 (Z方向平动)
    mat_tags['RubberRxy'],  # DOF 4 (绕X轴转动)
    mat_tags['RubberRxy'],  # DOF 5 (绕Y轴转动)
    mat_tags['RubberRz'],   # DOF 6 (绕Z轴转动)
    '-dir', 1, 2, 3, 4, 5, 6
)
```

## 关键参数

### 几何参数
```python
bearing_params = {
    "bearing_spacing": 0.5,         # 支座间距 (m)
    "deck_width": 13,               # 桥面宽度 (m)
    "block_transverse": True        # 是否设置横向挡块
}
```

### 材料参数
```python
bearing_material = {
    "G": 1200e3,                    # 动剪切模量 (N/m²)
    "A": 0.12,                      # 剪切面积 (m²)
    "te": 0.025,                    # 橡胶层总厚度 (m)
    "s": 2.0,                       # 形状系数
    "friction": True,               # 是否考虑摩擦滑动
    "friction_coef": 0.25           # 摩擦系数
}
```

### 刚度计算
```python
# 剪切刚度
K_shear = G * A / te               # 水平剪切刚度 (N/m)

# 抗压刚度  
E = 5.4 * G * s * s               # 压缩弹性模量
K_compression = E * A / t         # 竖向抗压刚度 (N/m)
```

## 材料模型类型

### 线性弹性模型
```python
# 适用于小变形分析
ops.uniaxialMaterial('Elastic', mat_tag, K_shear)
```

### 弹塑性摩擦模型
- 采用理想弹塑性本构，其中弹性-塑性段分别用于模拟剪切-滑动效应
- 水平向剪力到达临界静摩擦力时发生滑动
```python
# 考虑摩擦滑动效应
friction_force = friction_coef * axial_load
yield_displacement = friction_force / K_shear
ops.uniaxialMaterial('ElasticPP', mat_tag, K_shear, 
                     yield_displacement, -yield_displacement, 0.0)
```

## 与其他组件的关系

### 主梁连接
- 通过刚性连接（rigidLink）将支座节点与主梁节点关联
- 确保主梁节点的运动传递到支座节点

### 下部结构连接
- **桥墩处**: 连接到盖梁节点
- **桥台处**: 连接到桥台节点

### 横向约束
```python
# 横向挡块约束（可选）
if block_transverse:
    ops.equalDOF(girder_node, cap_end_node, 2)  # Y方向位移约束
```

## 分析功能

### 响应记录
- **内力记录**: 支座各方向力和弯矩时程
- **变形记录**: 支座各方向位移和转角时程
- **滞回特性**: 力-位移滞回曲线数据

### 事故检查

#### 支座剪切破坏
```python
# 破坏状态判定
gamma_limits = [0.7, 1.5, 2.5, 4.5]  # 剪切应变限值
displacement_limits = gamma_limits * rubber_thickness
```

#### 落梁事故
```python
# 基于支座滑移位移判定
falling_limit = 0.01 * (d0 + span_length * 100)  # (m)
```

### 性能评估
- **屈服位移**: 基于摩擦力和水平刚度计算
- **极限位移**: 考虑材料破坏的极限状态
- **滞回耗能**: 支座滞回曲线包围面积

## 使用示例

### 访问支座连接
```python
# 获取所有支座连接
for i, (deck_node, support_node) in enumerate(model.bearings['connections']):
    elem_tag = model.bearings['elements'][i]
    span_num = model.bearings['spans'][i]
    print(f"支座{i}: 单元{elem_tag}, 跨号{span_num}")
```

### 获取支座坐标
```python
import openseespy.opensees as ops

for i, (deck_node, support_node) in enumerate(model.bearings['connections']):
    x = ops.nodeCoord(deck_node, 1)  # 纵桥向坐标
    y = ops.nodeCoord(deck_node, 2)  # 横桥向坐标
    z = ops.nodeCoord(deck_node, 3)  # 竖向坐标
    print(f"支座{i}位置: ({x:.2f}, {y:.2f}, {z:.2f})")
```

### 支座材料信息查询
```python
# 查询弹塑性支座材料信息
if hasattr(model.params, 'bearing_materials'):
    for elem_tag, info in model.params.bearing_materials.items():
        print(f"支座{elem_tag}:")
        print(f"  轴向荷载: {info['axial_load']:.0f} N")
        print(f"  摩擦力: {info['friction_force']:.0f} N") 
        print(f"  屈服位移: {info['yield_disp']:.4f} m")
```

## 建模策略

### 桥墩处支座
1. 根据`bearing_spacing`参数确定支座数量和位置
2. 在主梁端部创建支座连接节点
3. 通过rigidLink连接到主梁节点
4. 连接到最近的盖梁节点

### 桥台处支座  
1. 在桥梁两端创建支座节点
2. 连接主梁端节点与桥台节点
3. 考虑桥台土体约束效应

### 横向挡块
1. 约束主梁端部与盖梁端部的横向相对位移
2. 通过equalDOF约束实现
3. 防止主梁横向滑移

## 注意事项

1. **节点对应**: 支座连接的两个节点必须位置重合或接近
2. **材料选择**: 根据分析需求选择线性或非线性材料模型
3. **轴向荷载**: 弹塑性模型需要准确的轴向荷载信息
4. **记录策略**: 选择代表性支座进行响应记录以减少数据量
5. **破坏判定**: 基于剪切应变或位移限值进行事故判定
6. **摩擦系数**: 根据接触面材料选择合适的摩擦系数值

## 相关模块

- `components/simply_supported_beam/isolator.py`: 支座建模实现
- `materials/rubber.py`: 橡胶支座材料定义
- `analysis/recorder/bearing.py`: 支座响应记录
- `analysis/check_accidents/bearing_failure.py`: 支座破坏检查
- `analysis/check_accidents/girder_falling.py`: 落梁事故检查
- `utils/bearing_axial_load.py`: 支座轴向荷载计算