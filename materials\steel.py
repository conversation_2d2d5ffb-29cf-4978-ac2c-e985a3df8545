import openseespy.opensees as ops


def set_steel(params, mat_tags):
    """设置钢筋材料本构模型
    
    创建纵向和横向钢筋材料，用于纤维截面建模
    
    Args:
        params: 桥梁参数对象
        mat_tags: 材料标签字典
    """
    # 纵向钢筋材料
    set_reinforcement(
        mat_tag=mat_tags["SteelLongitudinal"],
        steel_params=params.steel_materials["longitudinal"]
    )

    # 横向钢筋材料
    set_reinforcement(
        mat_tag=mat_tags["SteelTransverse"],
        steel_params=params.steel_materials["transverse"]
    )
    
    
def set_reinforcement(mat_tag, steel_params):
    """设置钢筋材料模型
    
    采用Steel02本构模型, 考虑应变硬化和Bauschinger效应
    
    Args:
        mat_tag (int): 材料标签
        steel_params (dict): 钢筋材料参数
    """
    fy = steel_params["fy"] * 1e6           # 屈服强度 (Pa)
    fu = steel_params["fu"] * 1e6           # 极限强度 (Pa)
    Es = steel_params["Es"] * 1e6           # 弹性模量 (Pa)
    eu = steel_params["eu"]                 # 极限应变

    # 计算应变硬化参数
    Eh = (fu - fy) / (eu - fy/Es)           # 硬化模量 (Pa)
    b = Eh / Es                             # 硬化强化率

    # 创建Steel02单轴材料
    ops.uniaxialMaterial('Steel02', mat_tag,
                        fy,                 # 屈服强度
                        Es,                 # 弹性模量
                        b,                  # 硬化强化率
                        20.0,               # 弹塑性转变参数R0
                        0.925,              # 转变曲线参数cR1
                        0.15)               # 转变曲线参数cR2
                        