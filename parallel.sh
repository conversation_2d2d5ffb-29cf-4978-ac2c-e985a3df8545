#!/bin/bash
# --------------------------------------------------------------------
# print runs
# ps aux | grep 'analyze_xuhui_multi_gm.py.*--gm_start=80.*--gm_end=90'
# --------------------------------------------------------------------
# kill runs
# ps -ef | grep 'analyze_xuhui_multi_gm.py.*--gm_start=80.*--gm_end=90' | grep -v grep | awk '{print $2}' | xargs kill
# --------------------------------------------------------------------


# Activate the conda environment first.
# Make sure you run 'source activate opensees' and 'chmod +x parallel.sh' in your terminal BEFORE running this script.
if [ -z "$CONDA_DEFAULT_ENV" ]; then
    echo "Error: Conda environment not activated."
    echo "Please run 'source activate opensees' before executing this script."
    exit 1
fi
echo "Using environment: $CONDA_DEFAULT_ENV"

# List all the indices to analyze
GM_START=50
GM_END=60
# INDICES=(13 14 16 22 76) # run in local
INDICES=(2 4 6 7 15 19 20 21 23 26 28 29 33 35 36 38 39 40 41 42 43 45 46 47 48 49 50 52 53 54 58 59 60 61 63 64 65 66 67 69 70 72 75 77)

# 定义并创建用于存放日志文件的目录
LOG_DIR="analysis_logs/GM${GM_START}-${GM_END}"
mkdir -p $LOG_DIR
echo "Log files will be saved in the '${LOG_DIR}' directory."

# Loop through the indices and start each analysis in the background
for i in "${INDICES[@]}"
do
   LOG_FILE="${LOG_DIR}/bridge_${i}.log"
   echo "Starting analysis for bridge_idx=${i}. Log at: ${LOG_FILE}"

   # >    将标准输出重定向到日志文件
   # 2>&1 将标准错误也重定向到同一个文件
   # &    后台运行
   python -u analyze_xuhui_multi_gm.py --bridge_idx=${i} --gm_start=${GM_START} --gm_end=${GM_END} > ${LOG_FILE} 2>&1 &
done

echo "All analysis jobs have been started in the background."
echo "Use 'tail -f ${LOG_DIR}/bridge_{i}.log' in a new terminal to see the running processes."

# The 'wait' command will cause the script to wait until all background jobs are finished.
wait

echo "All analysis jobs have completed."