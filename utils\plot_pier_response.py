"""
桥墩响应绘图工具

该模块提供绘制桥墩地震响应曲线的功能：
1. 弯矩-曲率曲线
2. 剪力-位移曲线
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from typing import Optional, Dict


def read_pier_response_data(results_dir: str, pier_id: str) -> Optional[Dict]:
    """
    读取桥墩响应数据
    
    参数:
        results_dir: 结果文件目录
        pier_id: 桥墩ID，格式如 "pier_x10.0_y0.0"
        
    返回:
        包含时间、位移、弯矩、曲率、剪力数据的字典，如果读取失败返回None
    """
    pier_data = {}
    
    # 构建文件路径
    disp_file = os.path.join(results_dir, f'pier_disp_{pier_id}.txt')
    moment_file = os.path.join(results_dir, f'pier_moment_{pier_id}.txt')
    curvature_file = os.path.join(results_dir, f'pier_curvature_{pier_id}.txt')
    
    # 读取位移数据
    if os.path.exists(disp_file):
        try:
            disp_data = np.loadtxt(disp_file)
            if disp_data.size > 0:
                if disp_data.ndim == 1:
                    disp_data = disp_data.reshape(1, -1)
                pier_data['time'] = disp_data[:, 0]
                pier_data['disp_x'] = disp_data[:, 1]  # X方向位移 (m)
                pier_data['disp_y'] = disp_data[:, 2]  # Y方向位移 (m)
                pier_data['disp_z'] = disp_data[:, 3]  # Z方向位移 (m)
        except Exception as e:
            print(f"读取位移文件 {disp_file} 时出错: {e}")
            return None
    else:
        print(f"位移文件不存在: {disp_file}")
        return None
            
    # 读取弯矩和剪力数据
    if os.path.exists(moment_file):
        try:
            moment_data = np.loadtxt(moment_file)
            if moment_data.size > 0:
                if moment_data.ndim == 1:
                    moment_data = moment_data.reshape(1, -1)

                if moment_data.shape[1] == 13:
                    # 13列格式: time + 6列第一单元 + 6列第二单元
                    pier_data['moment_x'] = moment_data[:, 4]  # X方向弯矩 (N·m)
                    pier_data['moment_y'] = moment_data[:, 5]  # Y方向弯矩 (N·m)
                    pier_data['shear_x'] = moment_data[:, 1]   # X方向剪力 (N)
                    pier_data['shear_y'] = moment_data[:, 2]   # Y方向剪力 (N)
                elif moment_data.shape[1] == 7:
                    # 7列格式: time + 6列内力
                    pier_data['moment_x'] = moment_data[:, 4]  # X方向弯矩 (N·m)
                    pier_data['moment_y'] = moment_data[:, 5]  # Y方向弯矩 (N·m)
                    pier_data['shear_x'] = moment_data[:, 1]   # X方向剪力 (N)
                    pier_data['shear_y'] = moment_data[:, 2]   # Y方向剪力 (N)
        except Exception as e:
            print(f"读取弯矩文件 {moment_file} 时出错: {e}")
            
    # 读取曲率数据
    if os.path.exists(curvature_file):
        try:
            curvature_data = np.loadtxt(curvature_file)
            if curvature_data.size > 0:
                if curvature_data.ndim == 1:
                    curvature_data = curvature_data.reshape(1, -1)
                # 截面变形: [轴向应变, 曲率_x, 曲率_y]
                pier_data['curvature_x'] = curvature_data[:, 1]  # X方向曲率 (1/m)
                pier_data['curvature_y'] = curvature_data[:, 2]  # Y方向曲率 (1/m)
        except Exception as e:
            print(f"读取曲率文件 {curvature_file} 时出错: {e}")
    
    if len(pier_data) == 0:
        return None
        
    return pier_data


def plot_moment_curvature(pier_data: Dict, direction: str = 'x', output_file: Optional[str] = None):
    """
    绘制弯矩-曲率曲线
    
    参数:
        pier_data: 桥墩响应数据字典
        direction: 方向 ('x' 或 'y')
        output_file: 输出文件路径, 如果为None则不保存
    """
    direction = direction.lower()
    
    # 检查数据是否存在
    moment_key = f'moment_{direction}'
    curvature_key = f'curvature_{direction}'
    
    if moment_key not in pier_data or curvature_key not in pier_data:
        print(f"缺少{direction.upper()}方向的弯矩或曲率数据，无法绘制")
        return
    
    # 获取数据并转换单位
    curvature = pier_data[curvature_key]  # 1/m
    moment = pier_data[moment_key] / 1e6  # N·m -> MN·m
    
    # 创建图形
    plt.figure(figsize=(6, 5), dpi=300, constrained_layout=True)
    plt.plot(curvature, moment, '-', linewidth=0.75, alpha=0.9)
    
    # 设置标签和标题
    plt.xlabel(f'曲率 (1/m)', fontsize=16)
    plt.ylabel(f'弯矩 (MN*m)', fontsize=16)
    plt.title(f'桥墩底部{direction.upper()}向弯矩-曲率曲线', fontsize=16, fontweight='bold')
    plt.grid(True, alpha=0.3)
    # plt.tight_layout()
    
    # 保存图形
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"弯矩-曲率曲线已保存至: {output_file}")
    
    plt.close()


def plot_shear_displacement(pier_data: Dict, direction: str = 'x', output_file: Optional[str] = None):
    """
    绘制剪力-位移曲线
    
    参数:
        pier_data: 桥墩响应数据字典
        direction: 方向 ('x' 或 'y')
        output_file: 输出文件路径，如果为None则不保存
    """
    direction = direction.lower()
    
    # 检查数据是否存在
    shear_key = f'shear_{direction}'
    disp_key = f'disp_{direction}'
    
    if shear_key not in pier_data or disp_key not in pier_data:
        print(f"缺少{direction.upper()}方向的剪力或位移数据，无法绘制")
        return
    
    # 获取数据并转换单位
    displacement = pier_data[disp_key] * 1000  # m -> mm
    shear = pier_data[shear_key] / 1000  # N -> kN
    
    # 创建图形
    plt.figure(figsize=(8, 6))
    plt.plot(displacement, shear, 'r-', linewidth=1.5, alpha=0.7)
    
    # 设置标签和标题
    plt.xlabel(f'位移 (mm)', fontsize=12)
    plt.ylabel(f'剪力 (kN)', fontsize=12)
    plt.title(f'桥墩顶部{direction.upper()}方向剪力-位移曲线', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    
    # 保存图形
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"剪力-位移曲线已保存至: {output_file}")
    
    plt.close()


def plot_pier_responses(results_dir: str, pier_id: Optional[str] = None):
    """
    绘制桥墩响应曲线（弯矩-曲率和剪力-位移）
    
    参数:
        results_dir: 结果文件目录
        pier_id: 桥墩ID, 如果为None则自动查找第一个桥墩
    """
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimSun']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['xtick.labelsize'] = 14    # x轴刻度标签字号
    plt.rcParams['ytick.labelsize'] = 14    # y轴刻度标签字号
    
    # 如果未指定pier_id，查找第一个桥墩
    if pier_id is None:
        pier_files = [f for f in os.listdir(results_dir) if f.startswith('pier_disp_')]
        if not pier_files:
            print(f"在 {results_dir} 中未找到桥墩数据文件")
            return
        # 提取pier_id
        pier_id = pier_files[0].replace('pier_disp_', '').replace('.txt', '')
        print(f"自动选择第一个桥墩: {pier_id}")
    
    # 读取桥墩数据
    pier_data = read_pier_response_data(results_dir, pier_id)
    if pier_data is None:
        print(f"无法读取桥墩 {pier_id} 的数据")
        return
    
    print(f"成功读取桥墩 {pier_id} 数据")
    
    # 绘制X方向弯矩-曲率曲线
    if 'moment_x' in pier_data and 'curvature_x' in pier_data:
        output_file = os.path.join(results_dir, f'{pier_id}_moment_curvature_x.png')
        plot_moment_curvature(pier_data, direction='x', output_file=output_file)
    
    # 绘制Y方向弯矩-曲率曲线
    if 'moment_y' in pier_data and 'curvature_y' in pier_data:
        output_file = os.path.join(results_dir, f'{pier_id}_moment_curvature_y.png')
        plot_moment_curvature(pier_data, direction='y', output_file=output_file)
    
    # 绘制X方向剪力-位移曲线
    if 'shear_x' in pier_data and 'disp_x' in pier_data:
        output_file = os.path.join(results_dir, f'{pier_id}_shear_displacement_x.png')
        plot_shear_displacement(pier_data, direction='x', output_file=output_file)
    
    # 绘制Y方向剪力-位移曲线
    if 'shear_y' in pier_data and 'disp_y' in pier_data:
        output_file = os.path.join(results_dir, f'{pier_id}_shear_displacement_y.png')
        plot_shear_displacement(pier_data, direction='y', output_file=output_file)
    
    print(f"桥墩响应曲线绘制完成")


if __name__ == "__main__":
    # 测试代码
    import sys
    
    if len(sys.argv) > 1:
        results_dir = sys.argv[1]
        pier_id = sys.argv[2] if len(sys.argv) > 2 else None
        plot_pier_responses(results_dir, pier_id)
    else:
        print("用法: python utils/plot_pier_response.py <results_dir> [pier_id]")

