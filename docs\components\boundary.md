# Boundary对象技术文档

## 概述

Boundary对象负责桥梁结构分析模型中边界条件的定义和施加，包括桥墩底部约束、桥台边界条件以及土-结构相互作用建模。该对象支持固定约束和弹性约束两种模式，为结构分析提供合理的边界条件。

## 数据结构

### 基本结构
```python
# 边界条件通过OpenSees约束命令实现，无独立数据结构
# 相关信息存储在各组件中：
model.piers['elements']      # 桩-土相互作用单元
model.abutments['elements']  # 桥台边界单元
```

### 约束类型组织

#### 桥墩边界条件
```python
# 固定约束模式
ops.fix(pier_bottom_node, 1, 1, 1, 1, 1, 1)

# 土-结构相互作用模式  
ops.fix(pier_bottom_node, 0, 1, 1, 1, 1, 1)  # X方向自由
# + zeroLength单元连接桩基节点
```

#### 桥台边界条件
```python
# 桥台节点约束
ops.fix(abutment_node, 1, 1, 1, 1, 1, 1)  # 固定模式
ops.fix(abutment_node, 0, 1, 1, 1, 1, 1)  # SSI模式

# 主梁端部横向约束（挡块效应）
ops.fix(deck_end_node, 0, 1, 0, 0, 0, 0)
```

## 关键参数

### 土-结构相互作用参数
```python
pile_soil = {
    "enable_ssi": True,                 # 是否启用SSI
    "m_value": 5e6,                     # 水平抗力系数比例系数 (N/m⁴)
    "group_effect_factor": 0.8,         # 群桩效应系数
    "pile_diameter_pier": 0.6,          # 桥墩桩直径 (m)
    "pile_number_pier": 6,              # 桥墩桩数量
    "pile_diameter_abutment": 0.8,      # 桥台桩直径 (m)
    "pile_number_abutment": 8           # 桥台桩数量
}
```

### 桥台回填土参数
```python
abutment = {
    "gap": 0.05,                        # 初始间隙 (m)
    "backfill_stiffness": 50e6,         # 回填土刚度 (N/m)
    "backfill_strength": 500e3,         # 回填土强度 (N)
    "passive_pressure_coef": 3.0        # 被动土压力系数
}
```

## 边界条件类型

### 桥墩底部边界

#### 固定约束模式 (`enable_ssi = False`)
```python
# 完全固定约束
ops.fix(pier_bottom_node, 1, 1, 1, 1, 1, 1)
```
- **适用场景**: 刚性地基、简化分析
- **约束自由度**: 全部6个DOF
- **计算效率**: 高
- **精度**: 偏保守

#### 土-结构相互作用模式 (`enable_ssi = True`)
```python
# 部分约束 + 弹性连接
ops.fix(pier_bottom_node, 0, 1, 1, 1, 1, 1)  # X方向自由

# 创建桩基节点和连接单元
pile_node = model._next_tag('node')
ops.node(pile_node, *pier_coords)
ops.fix(pile_node, 1, 1, 1, 1, 1, 1)

# zeroLength单元模拟桩-土相互作用
ops.element('zeroLength', elem_tag, pier_bottom_node, pile_node,
            '-mat', pile_material_tag, '-dir', 1)
```

### 桥台边界条件

#### 桥台节点约束
- **固定模式**: 完全固定所有DOF
- **SSI模式**: X方向通过回填土弹簧约束

#### 回填土弹簧建模
```python
# 创建土体节点
soil_node = model._next_tag('node')
ops.node(soil_node, *abutment_coords)
ops.fix(soil_node, 1, 1, 1, 1, 1, 1)

# 回填土材料（考虑间隙和非线性）
ops.uniaxialMaterial('ElasticPPGap', backfill_mat_tag,
                     backfill_stiffness, backfill_strength, 
                     gap, -backfill_strength)

# zeroLength单元连接
ops.element('zeroLength', elem_tag, abutment_node, soil_node,
            '-mat', backfill_mat_tag, '-dir', 1)
```

#### 横向挡块约束
```python
# 主梁端部横向约束
ops.fix(deck_start_node, 0, 1, 0, 0, 0, 0)  # 左端
ops.fix(deck_end_node, 0, 1, 0, 0, 0, 0)    # 右端
```

## 材料模型

### 桩基材料模型
```python
# 基于m法计算桩基刚度
def calculate_pile_stiffness(pile_params):
    m = pile_params['m_value']
    D = pile_params['pile_diameter']
    n = pile_params['pile_number']
    group_factor = pile_params['group_effect_factor']
    
    # 单桩水平刚度
    single_pile_k = 0.9 * (m * D)**0.5 * D**2
    
    # 群桩刚度
    group_k = single_pile_k * n * group_factor
    
    return group_k
```

### 回填土材料模型
```python
# 弹塑性间隙材料
ops.uniaxialMaterial('ElasticPPGap', mat_tag,
    E,          # 弹性刚度
    fy_pos,     # 正向屈服强度  
    gap_pos,    # 正向间隙
    fy_neg,     # 负向屈服强度
    eta=0.0     # 硬化参数
)
```

## 与其他组件的关系

### 桥墩连接
- 桥墩底部节点施加边界约束
- SSI模式下通过zeroLength单元连接桩基
- 桩基材料考虑土体刚度和群桩效应

### 桥台连接
- 桥台节点直接施加约束
- 回填土通过弹簧单元模拟
- 主梁端部设置横向挡块约束

### 主梁约束
- 端部节点横向约束模拟挡块
- 不直接约束主梁其他自由度
- 通过支座传递竖向和纵向约束

## 分析功能

### 约束反力记录
```python
# 记录约束反力
ops.recorder('Node', '-file', 'reactions.out',
             '-node', constrained_nodes,
             '-dof', 1, 2, 3, 'reaction')
```

### 土-结构相互作用分析
- **桩基响应**: 记录桩顶位移和桩身内力
- **土体反力**: 监测土体对结构的约束反力
- **非线性效应**: 考虑土体非线性和间隙效应

### 边界条件验证
- **约束检查**: 验证约束是否正确施加
- **反力平衡**: 检查整体力平衡
- **位移合理性**: 验证边界位移的合理性

## 使用示例

### 查看约束信息
```python
# 获取节点约束状态
def check_node_constraints(node_tag):
    # OpenSees没有直接查询约束的命令
    # 可通过施加小荷载检查约束DOF
    pass
```

### 获取约束反力
```python
# 获取节点反力
def get_node_reactions(node_tag):
    reactions = []
    for dof in range(1, 7):
        try:
            reaction = ops.nodeReaction(node_tag, dof)
            reactions.append(reaction)
        except:
            reactions.append(0.0)
    return reactions
```

### SSI刚度计算
```python
# 计算桩基水平刚度
def calculate_pier_pile_stiffness(model, pier_idx):
    pile_params = model.params.pile_soil
    
    m = pile_params['m_value']
    D = pile_params['pile_diameter_pier']
    n = pile_params['pile_number_pier']
    group_factor = pile_params['group_effect_factor']
    
    # 单桩刚度 (基于m法)
    single_k = 0.9 * (m * D)**0.5 * D**2
    
    # 群桩刚度
    group_k = single_k * n * group_factor
    
    return group_k
```

## 建模策略

### 边界条件选择
1. **简化分析**: 使用固定约束，计算效率高
2. **精细分析**: 使用SSI模型，考虑土体柔性
3. **参数敏感性**: 对比不同边界条件的影响

### SSI建模要点
1. 合理选择土体参数（m值、群桩系数）
2. 考虑桩基几何和数量
3. 验证土体刚度的合理性

### 桥台建模要点
1. 回填土刚度基于土体性质确定
2. 考虑初始间隙和接触非线性
3. 横向挡块约束防止横向滑移

## 注意事项

1. **约束完整性**: 确保结构无刚体位移模式
2. **SSI参数**: 土体参数应基于地质勘察确定
3. **数值稳定性**: 避免过大的刚度比导致数值问题
4. **边界合理性**: 边界条件应符合实际工程情况
5. **约束反力**: 监测约束反力的合理性
6. **收敛性**: SSI模型可能影响求解收敛性

## 相关模块

- `components/simply_supported_beam/boundary.py`: 边界条件实现
- `materials/pile.py`: 桩基材料定义
- `materials/backfill.py`: 回填土材料定义
- `analysis/recorder/boundary.py`: 边界响应记录
- `utils/ssi_stiffness.py`: SSI刚度计算工具