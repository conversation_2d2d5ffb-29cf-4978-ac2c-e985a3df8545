import numpy as np


def calculate_target_curvature(params, target_strain=0.004):
    """
    预先计算截面边缘混凝土压应变达到目标值时的截面曲率
    考虑钢筋对中性轴位置和力平衡的影响

    参数:
        params: 桥梁参数对象
        target_strain: 目标压应变 (默认0.004)

    返回:
        float: 目标曲率 (1/m)
    """
    if params.num_spans == 1:
        return 0.0

    # 获取截面参数
    D = params.pier_section["diameter"]  # 桥墩直径 (m)
    cover = params.pier_section["concrete_cover"]  # 保护层厚度 (m)

    # 获取材料参数
    fc = params.concrete_materials["core"]["fc"] * 1e6  # 核心区混凝土强度 (Pa)
    fy = params.steel_materials["longitudinal"]["fy"] * 1e6  # 钢筋屈服强度 (Pa)
    Es = params.steel_materials["longitudinal"]["Es"] * 1e6  # 钢筋弹性模量 (Pa)

    # 获取钢筋参数
    rho = params.pier_section['longitudinal_bars']['rho']  # 配筋率
    bar_dia = params.pier_section['longitudinal_bars']['diameter']  # 钢筋直径

    # 获取轴力
    P = params.pier_section.get('axial_force', 0)  # 轴力 (N)

    # 截面几何参数
    # A_gross = np.pi * (D/2)**2  # 毛截面面积 (m²)

    # 钢筋重心半径（假设均匀分布在圆周上）
    d = D/2 - cover - bar_dia/2  # 钢筋重心到截面中心的距离

    # 迭代求解中性轴深度，考虑钢筋作用
    c = solve_neutral_axis_with_steel(params, D, cover, fc, fy, Es, rho, P, target_strain, d)

    # 计算目标曲率
    curvature = target_strain / c

    # 验证钢筋应力状态
    steel_strain = (d - c) / c * target_strain  # 钢筋应变
    # steel_stress = min(Es * abs(steel_strain), fy)  # 钢筋应力

    # print(f"目标压应变 {target_strain:.4f} 对应的截面曲率: {curvature:.6f} (1/m)")
    # print(f"  - 中性轴深度: {c:.3f} m")
    # print(f"  - 钢筋应变: {steel_strain:.6f}")
    # print(f"  - 钢筋应力: {steel_stress/1e6:.1f} MPa")
    # print(f"  - 轴压比: {P/(A_gross*fc):.4f}")

    return curvature


def solve_neutral_axis_with_steel(params, D, cover, fc, fy, Es, rho, P, target_strain, d):
    """
    考虑钢筋作用的中性轴深度求解
    基于力平衡和应变协调条件

    参数:
        D: 截面直径 (m)
        cover: 保护层厚度 (m)
        fc: 混凝土强度 (Pa)
        fy: 钢筋屈服强度 (Pa)
        Es: 钢筋弹性模量 (Pa)
        rho: 配筋率
        P: 轴力 (N)
        target_strain: 目标压应变
        d: 钢筋重心半径 (m)

    返回:
        float: 中性轴深度 (m)
    """
    # 离散化钢筋位置
    num_bars = params.pier_section['longitudinal_bars']['number']
    bar_dia = params.pier_section['longitudinal_bars']['diameter']  # 钢筋直径
    A_bar = np.pi * (bar_dia/2)**2
    d_ring = D/2 - cover - bar_dia/2 # 钢筋环半径
    bar_angles = np.linspace(0, 2*np.pi, num_bars, endpoint=False)
    bar_y_coords = d_ring * np.cos(bar_angles) # 每根钢筋的y坐标
    
    def force_equilibrium(c):
        """力平衡方程"""
        if c <= 0: return P + 1e9 # 返回一个大的正数，避免c为负
        
        # 1. 计算混凝土压力合力 Cc（简化为矩形应力块）
        beta1 = 0.85 if fc/1e6 <= 28 else max(0.65, 0.85 - 0.05*(fc/1e6 - 28)/7)
        a = beta1 * c  # 等效矩形应力块高度

        # 计算混凝土压应力合力
        if a <= D:
            # 压应力块在截面内
            theta = 2 * np.arccos(max(-1, min(1, (D/2 - a) / (D/2))))  # 压应力块对应的圆心角
            A_comp = (D/2)**2 * (theta - np.sin(theta)) / 2  # 压应力块面积
            Cc = 0.85 * fc * A_comp  # 混凝土压力合力
        else:
            # 整个截面受压
            Cc = 0.85 * fc * np.pi * (D/2)**2

        # 2. 循环计算钢筋合力
        Ts = 0
        for y_i in bar_y_coords:
            # 假设中性轴在y=c处 (从截面顶部算起)，截面中心在y=D/2
            # 钢筋y坐标是相对于中心，需转换
            y_i_from_top = D/2 - y_i
            
            strain_i = target_strain * (c - y_i_from_top) / c
            
            if strain_i > 0: # 受拉
                stress_i = min(Es * strain_i, fy)
            else: # 受压
                stress_i = max(Es * strain_i, -fy) # 假设拉压同性
            
            Ts += stress_i * A_bar

        # 力平衡方程：Cc - Ts - P = 0
        return Cc - Ts - P

    # 简化的二分法求解中性轴深度
    c_min = cover + 0.01  # 最小值
    c_max = D - cover - 0.01  # 最大值

    # 检查边界条件
    f_min = force_equilibrium(c_min)
    f_max = force_equilibrium(c_max)

    if f_min * f_max > 0:
        # 如果边界值同号，使用经验公式
        n = P / (np.pi * (D/2)**2 * fc) if P > 0 else 0
        c_solution = D/2 * (0.3 + 0.4*n + 0.1*rho)  # 考虑配筋率的经验公式
        c_solution = max(c_min, min(c_solution, c_max))
    else:
        # 二分法求解
        for _ in range(20):  # 最多迭代20次
            c_mid = (c_min + c_max) / 2
            f_mid = force_equilibrium(c_mid)

            if abs(f_mid) < 1000:  # 收敛判据：力平衡误差小于1kN
                c_solution = c_mid
                break

            if f_min * f_mid < 0:
                c_max = c_mid
                f_max = f_mid
            else:
                c_min = c_mid
                f_min = f_mid
        else:
            c_solution = (c_min + c_max) / 2

    return c_solution
