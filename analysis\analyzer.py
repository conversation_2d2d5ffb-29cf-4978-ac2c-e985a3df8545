import os
import uuid
import numpy as np
import matplotlib.pyplot as plt
import openseespy.opensees as ops

from datetime import datetime
from gm.read_record import ReadRecord
from utils.bearing_axial_load import update_bearing_materials

# Import summarize functions
from analysis.summarize import (
    summarize_bearing_relative_displacements as summarize_bearing_disps,
    summarize_pier as summarize_pier,
    summarize_abutment_displacements as summarize_abutment_disps,
    summarize_deck_displacements as summarize_deck_disps
)

# Import accident checking functions
from analysis.check_accidents import check_girder_falling, check_bearing_failure

# Import recorder functions
from analysis.recorder import (
    setup_pier_recorders,
    setup_abutment_recorders,
    setup_deck_recorders,
    setup_bearing_recorders,
    read_abutment_displacement_data,
    read_deck_displacement_data,
    remove_recorders
)

# Import dynamic analysis engine
from analysis.dynamic_engine import DynamicAnalysisEngine
from analysis.recorder.bearing import find_bearings_closest_to_y0

# Import plotting utilities
from utils.plot_pier_response import plot_pier_responses
from utils.plot_bearing_response import plot_bearing_responses


class BridgeAnalyzer:
    """桥梁地震响应分析器
    
    提供模态分析、静力分析和非线性动力时程分析功能
    支持简支梁桥的地震响应计算和事故判定
    """
    
    def __init__(self, model):
        self.model = model
        self.frequencies = []                    # 固有频率存储 (Hz)
        self.bearing_relative_disps = {}         # 支座相对位移数据存储
        self.recorder_info = {}                  # 记录器信息存储
        self.pier_displacement = False           # 桥墩位移超限事故标志
        self.results_dir = None                  # 当前分析结果目录
        self._init_analysis()


    def _init_analysis(self):
        """初始化分析基本参数"""
        self.damping_ratio = self.model.params.damping_ratio

        # 地震分析参数
        self.dt, self.npts = None, None
        self.direction = 1                       # 地震激励方向: 1-纵向, 2-横向, 3-竖向（当前仅支持1）
        self.gm_time_series_tag = 2              # 地震动时间序列标签
        self.seismic_pattern_tag = 2             # 地震荷载模式标签


    def _setup_modal_analysis(self):
        """配置模态分析求解器
        
        使用一致质量矩阵进行特征值分析，提高模态分析精度
        """
        ops.wipeAnalysis()
        ops.system('FullGeneral')               # 特征值分析需要完全存储矩阵
        ops.numberer('RCM')                     # RCM节点编号算法
        ops.constraints('Transformation')       # 约束处理方法
        ops.algorithm('Linear')                 # 线性求解算法

        # 配置质量矩阵提取器
        try:
            ops.integrator('GimmeMCK', 1.0, 0.0, 0.0, 1)  # 一致质量矩阵
        except Exception:
            ops.integrator('GimmeMCK', 1.0, 0.0, 0.0)     # 降级使用集中质量矩阵

        ops.analysis('Transient')  # 伪时程分析设置


    def _setup_static_analysis(self):
        """配置静力分析求解器"""
        ops.wipeAnalysis()
        ops.system('BandGeneral')               # 带状存储系统
        ops.constraints('Transformation')       # 约束处理方法
        ops.numberer('RCM')                     # RCM节点编号算法
        ops.test('NormDispIncr', 1.0e-5, 50)    # 收敛判据: 位移增量范数
        ops.algorithm('Newton')                 # Newton-Raphson算法
        ops.integrator('LoadControl', 0.1)      # 荷载控制积分器
        ops.analysis('Static')                  # 静力分析


    def _setup_dynamic_analysis(self):
        """配置非线性动力时程分析求解器"""
        ops.wipeAnalysis()
        ops.system('BandGeneral')               # 带状存储系统
        ops.numberer('RCM')                     # RCM节点编号算法
        ops.constraints('Transformation')       # 约束处理方法
        ops.test('NormDispIncr', 1e-6, 400)     # 收敛判据: 位移增量范数
        ops.algorithm('NewtonLineSearch')       # Newton线搜索算法
        ops.integrator('Newmark', 0.5, 0.25)    # Newmark-β积分器 (γ=0.5, β=0.25)
        ops.analysis('Transient')               # 瞬态分析


    def modal(self, num_modes=3):
        """执行模态分析
        
        Args:
            num_modes (int): 提取的模态数量, 默认为3
        """
        self._setup_modal_analysis()

        # 执行特征值分析
        eigen_values = ops.eigen(num_modes)

        # 计算并存储频率
        self.frequencies = self._calc_frequencies(eigen_values)

        # 设置瑞利阻尼
        self._set_rayleigh()


    def static(self, update_bearing=False):
        """执行静力分析
        
        Args:
            update_bearing (bool): 是否更新支座材料属性, 默认为False
        """
        self._setup_static_analysis()
        self._perform_static_analysis()

        # 计算节点反力
        ops.reactions()

        # 更新支座材料属性（用于考虑轴向荷载影响）
        if update_bearing:
            update_bearing_materials(self.model)


    def _perform_static_analysis(self):
        """执行静力分析
        
        采用10步荷载控制分析, 确保结构达到平衡状态
        """
        try:
            ops.analyze(10)
        except Exception as e:
            print(f"静力分析失败: {e}")
            return


    def dynamic(self, h='gm/elCentro.at2', pga=0.15, dt_analysis=0.01, dt_output=None):
        """执行非线性动力时程分析

        Args:
            h: 地震记录，支持文件路径(.at2)或numpy数组
            pga (float): 峰值地面加速度 (g)
            dt (float): 分析时间步长 (s)
            dt_output (float, optional): 输出时间步长 (s)，默认与dt相同

        Returns:
            dict: 分析统计信息
        """
        # 预分析：静力分析获取支座轴向荷载
        if not hasattr(self.model, 'bearing_axial_loads'):
            self.static(update_bearing=True)
        ops.reset()
        self.static()
        print("静力分析完成")

        # 地震动力分析
        ops.loadConst('-time', 0.0)
        self._setup_dynamic_analysis()
        self._apply_ground_motion(h, pga)

        # 设置输出步长
        if dt_output is None:
            dt_output = self.dt

        # 参数验证
        if self.dt > dt_output:
            raise ValueError(f"分析步长 ({self.dt}s) 不能大于输出步长 ({dt_output}s)")
        if dt_output % self.dt > 1e-9 and abs(dt_output % self.dt - self.dt) > 1e-9:
            print(f"警告: 输出步长 ({dt_output}s) 不是分析步长 ({self.dt}s) 的整数倍，可能导致输出时间点不精确")

        # 创建时间戳命名的结果目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.results_dir = os.path.join('results', timestamp)
        os.makedirs(self.results_dir, exist_ok=True)
        print(f"结果将保存至: {self.results_dir}")

        # 设置响应记录器 - 记录桥墩、桥台、主梁节点和支座
        self.recorder_info = setup_pier_recorders(self.model, dt_output, output_dir=self.results_dir, recorder_info=None)
        self.recorder_info = setup_abutment_recorders(self.model, dt_output, output_dir=self.results_dir, recorder_info=self.recorder_info)
        self.recorder_info = setup_deck_recorders(self.model, dt_output, output_dir=self.results_dir, recorder_info=self.recorder_info)
        self.recorder_info = setup_bearing_recorders(self.model, dt_output, output_dir=self.results_dir, recorder_info=self.recorder_info)

        # 创建动力分析引擎
        engine = DynamicAnalysisEngine(dt_analysis, dt_output)

        # 执行动力分析
        target_time = self.dt * self.npts
        self.analysis_stats = engine.run_analysis(
            target_time=target_time,
            record_callback=self._record_responses,
            try_algorithms_callback=self._try_different_algorithms
        )

        # 输出各组件相对位移数据摘要
        print("\n"+"="*60)
        print("响应数据处理\n"+"="*60)
        # 支座相对位移
        summarize_bearing_disps(self.bearing_relative_disps, output_dir=self.results_dir)

        # 桥墩相对位移 - 使用记录器数据
        if self.model.piers['nodes']:
            # 使用记录器数据并检查桥墩位移超限事故
            self.pier_displacement = summarize_pier(self.recorder_info, model=self.model, output_dir=self.results_dir)

        # 桥台节点位移 - 使用记录器数据
        abutment_disps = read_abutment_displacement_data(self.recorder_info)
        summarize_abutment_disps(abutment_disps, output_dir=self.results_dir)

        # 主梁节点位移 - 使用记录器数据
        deck_disps = read_deck_displacement_data(self.recorder_info)
        summarize_deck_disps(deck_disps, output_dir=self.results_dir)

        print("\n"+"="*60)
        print("事故检查\n"+"="*60)
        # 检查是否发生落梁事故
        self.check_accidents()

        # 移除记录器
        remove_recorders(self.recorder_info)

        # 绘制桥墩响应曲线
        if self.model.piers['nodes'] and self.results_dir:
            print("\n"+"="*60)
            print("绘制桥墩响应曲线\n"+"="*60)
            try:
                plot_pier_responses(self.results_dir)
            except Exception as e:
                print(f"绘制桥墩响应曲线时出错: {e}")

        # 绘制支座响应曲线
        if self.model.bearings['elements'] and self.results_dir:
            print("\n"+"="*60)
            print("绘制支座响应曲线\n"+"="*60)
            try:
                plot_bearing_responses(self.results_dir)
            except Exception as e:
                print(f"绘制支座响应曲线时出错: {e}")

        ops.wipe()

        # 返回分析统计信息
        return self.analysis_stats


    def check_accidents(self):
        """检查地震事故发生情况
        
        检查三类主要事故：落梁、支座剪切破坏、桥墩位移超限
        
        Returns:
            bool: 是否发生任何事故
        """
        # 检查落梁事故
        girder_falling = check_girder_falling(
            self.bearing_relative_disps, model=self.model, params=self.model.params)

        # 检查支座剪切破坏事故
        bearing_failure = check_bearing_failure(
            self.bearing_relative_disps, model=self.model, params=self.model.params)

        # 桥墩位移超限事故检查结果已在summarize_pier函数中完成
        return girder_falling or self.pier_displacement or bearing_failure


    def _calc_frequencies(self, eigen_values):
        """将特征值转换为自振频率
        
        Args:
            eigen_values (list): 特征值列表
            
        Returns:
            list: 自振频率列表 (Hz)
        """
        freqs = [np.sqrt(abs(value))/(2*np.pi) for value in eigen_values if value > 0]
        for i, freq in enumerate(freqs[:3]):
            print(f"第{i+1}阶频率: {freq:.2f} Hz")
        return freqs


    def _set_rayleigh(self):
        """设置瑞利阻尼
        
        基于前两阶模态频率计算瑞利阻尼系数
        """
        omega1 = 2 * np.pi * self.frequencies[0]    # 第一阶圆频率 (rad/s)
        omega2 = 2 * np.pi * self.frequencies[1]    # 第二阶圆频率 (rad/s)
        
        # 计算瑞利阻尼系数
        xi = self.damping_ratio
        alpha = (2 * xi * omega1 * omega2) / (omega1 + omega2)  # 质量比例阻尼系数
        beta = (2 * xi) / (omega1 + omega2)                     # 刚度比例阻尼系数

        ops.rayleigh(alpha, beta, 0.0, 0.0)


    def _apply_ground_motion(self, h='gm/elCentro.at2', pga=0.15, dt_time_series=None):
        """施加地震动激励

        Args:
            h: 地震记录, 支持PEER格式文件(.at2)或numpy数组
            pga (float): 峰值地面加速度 (g)
            dt (float): 时间步长 (s)
        """
        # 确保结果目录存在
        os.makedirs('gm', exist_ok=True)
        os.makedirs('results', exist_ok=True)
                
        # 为当前进程生成唯一的文件名
        process_id = uuid.uuid4().hex
        unique_suffix = f"_{process_id}" # 使用UUID作为唯一后缀
        
        temp_file = f'gm/temp{unique_suffix}.dat'

        try:
            # 处理不同格式的地震动输入
            if isinstance(h, str):
                if h.lower().endswith('.at2'):
                    # 读取PEER格式地震记录文件
                    self.dt, self.npts = ReadRecord(h, temp_file)

                    # 定义地震动时间序列
                    ops.timeSeries(
                        'Path', self.gm_time_series_tag,
                        '-dt', self.dt,
                        '-time', list(np.arange(0, self.npts*self.dt, self.dt)),
                        '-filePath', temp_file,
                        '-factor', self.model.params.gravity * pga
                    )
            else:
                # 处理numpy数组格式的地震记录
                if not isinstance(h, np.ndarray):
                    h = np.array(h)
                
                self.dt = dt_time_series
                h = h / np.abs(h).max() * pga           # 缩放至目标PGA
                h[0] = 0                                # 确保起始加速度为零

                # 保存处理后的地震记录
                np.savetxt(temp_file, h)

                # 绘制地震动时程曲线
                plt.figure(figsize=(12, 3))
                plt.plot(np.arange(0, len(h)*self.dt, self.dt)[:len(h)], h)
                plt.xlabel('Time (s)')
                plt.ylabel('Acceleration (g)')
                plt.grid(True)
                plt.savefig('gm/earthquake_record.png', dpi=300)
                plt.close()
                self.npts = len(h)

                # 定义地震动时间序列
                ops.timeSeries(
                    'Path', self.gm_time_series_tag,
                    '-dt', self.dt,
                    '-time', list(np.arange(0, self.npts*self.dt, self.dt)),
                    '-filePath', temp_file,
                    '-factor', self.model.params.gravity
                )

            # 定义地震荷载模式（均匀激励）
            ops.pattern('UniformExcitation',
                        self.seismic_pattern_tag,
                        self.direction,                     # 激励方向
                        '-accel', self.gm_time_series_tag)  # 加速度时间序列
        finally:
            # 删除临时文件
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except OSError as e:
                    print(f"Error removing temp file {temp_file}: {e}")

    def _record_responses(self, current_time):
        """记录结构响应数据
        
        Args:
            current_time (float): 当前时间
        """
        # 记录支座相对位移（其他响应通过recorder记录）
        self._record_bearing_relative_displacements(current_time)


    def _record_bearing_relative_displacements(self, time):
        """记录代表性支座的相对位移
        
        选择每个x坐标处最靠近y=0的支座进行记录，减少数据量
        
        Args:
            time (float): 当前时间
        """
        # 初始化时间步数据
        if time not in self.bearing_relative_disps:
            self.bearing_relative_disps[time] = []

        # 获取代表性支座索引（首次调用时计算）
        if not hasattr(self, '_selected_bearing_indices'):
            self._selected_bearing_indices = find_bearings_closest_to_y0(self.model)

        # 遍历选定的代表性支座
        for i in self._selected_bearing_indices:
            deck_node, support_node = self.model.bearings['connections'][i]

            # 获取节点坐标
            x_coord = ops.nodeCoord(deck_node, 1)
            y_coord = ops.nodeCoord(deck_node, 2)

            # 获取主梁节点位移
            deck_disp_x = ops.nodeDisp(deck_node, 1)
            deck_disp_y = ops.nodeDisp(deck_node, 2)
            deck_disp_z = ops.nodeDisp(deck_node, 3)

            # 获取支承节点位移
            support_disp_x = ops.nodeDisp(support_node, 1)
            support_disp_y = ops.nodeDisp(support_node, 2)
            support_disp_z = ops.nodeDisp(support_node, 3)

            # 计算支座相对位移
            rel_disp_x = deck_disp_x - support_disp_x
            rel_disp_y = deck_disp_y - support_disp_y
            rel_disp_z = deck_disp_z - support_disp_z

            # 获取支座属性信息
            span_num = self.model.bearings['spans'][i]
            elem_tag = self.model.bearings['elements'][i]

            # 存储支座相对位移数据
            self.bearing_relative_disps[time].append({
                'bearing_idx': i,
                'span': span_num,
                'x_coord': x_coord,
                'y_coord': y_coord,
                'rel_disp_x': rel_disp_x,
                'rel_disp_y': rel_disp_y,
                'rel_disp_z': rel_disp_z,
                'deck_node': deck_node,
                'support_node': support_node,
                'elem_tag': elem_tag
            })


    def _try_different_algorithms(self, dt_current):
        """尝试备选算法解决收敛问题
        
        按优先级顺序尝试不同的求解算法和收敛准则
        
        Args:
            dt_current (float): 当前时间步长
            
        Returns:
            bool: 是否成功收敛
        """
        # 备选算法
        algorithms = [
            # {'name': 'KrylovNewton',   'test': ('NormDispIncr', 1e-5, 400)},
            # {'name': 'BFGS',           'test': ('NormDispIncr', 1e-5, 400)},
            # {'name': 'ModifiedNewton', 'test': ('NormDispIncr', 1e-5, 300), 'params': ('-initial')},
        ]

        for alg in algorithms:
            try:
                # 设置求解算法
                if 'params' in alg:
                    ops.algorithm(alg['name'], *alg['params'])
                else:
                    ops.algorithm(alg['name'])

                # 设置收敛准则
                ops.test(*alg['test'])

                # 尝试分析当前时间步
                if ops.analyze(1, dt_current) == 0:
                    return True
            except Exception as e:
                print(f"算法 {alg['name']} 失败: {e}")

        return False
