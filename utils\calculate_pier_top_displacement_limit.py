import numpy as np


def pier_disp_limit(params):
    """
    计算圆形钢筋混凝土桥墩在不同性能水平下的顶部位移限值
    
    该函数基于截面曲率计算桥墩的屈服位移和极限位移。计算模型将桥墩简化为
    一端固定的悬臂梁，总位移由弹性屈服位移和塑性铰转动引起的塑性位移组成。

    参数:
    ----------
    params : object
        一个包含所有计算所需参数的对象或字典。预期属性包括:
        - num_spans (int): 桥梁的跨数。单跨桥梁不计算墩顶位移。
        - pier_section (dict): 桥墩截面属性。
            - 'diameter' (float): 桥墩圆形截面直径 (m)。
            - 'phiu' (float): 截面极限曲率 (1/m)。
            - 'curvature_ds' (list[float]): 对应各破坏状态(DS1, DS2, DS3)的截面曲率 (1/m)。
            - 'longitudinal_bars' (dict): 纵筋信息。
                - 'diameter' (float): 纵筋直径 (m)。
        - steel_materials (dict): 钢筋材料属性。
            - 'longitudinal' (dict): 纵筋材料属性。
                - 'fy' (float): 钢筋的屈服强度 (Pa, e.g., N/m^2)。
                - 'Es' (float): 钢筋的弹性模量 (Pa, e.g., N/m^2)。
        - pier_heights (list[float] or np.ndarray): 各个桥墩的高度列表 (m)。
    
    返回:
    ----------
    list[float]
        一个包含三个值的列表，分别对应破坏状态 DS1, DS2, DS3 的桥墩顶部位移限值 (m)。
        如果为单跨桥，则返回 [0, 0, 0]。

    计算步骤:
    ----------
    1.  计算钢筋屈服应变 (ey) 和截面屈服曲率 (phiy)。
    2.  根据屈服曲率计算桥墩的屈服位移 (d_y)。
    3.  根据规范公式计算塑性铰长度 (lp)。
    4.  对于每个破坏状态 (DS1, DS2, DS3):
        a. 获取对应的截面曲率 (phi_ds)。
        b. 计算塑性铰转角 (theta_ds)。
        c. 计算总位移限值 (d_limit = d_y + 塑性位移)。
    5.  打印详细的中间和最终结果。
    """
    # 对于单跨桥梁，墩高为0，不进行位移计算
    if params.num_spans == 1:
        return [0.0, 0.0, 0.0]
    
    # --- 1. 提取参数 ---
    pier  = params.pier_section
    steel = params.steel_materials["longitudinal"]
    H     = np.mean(params.pier_heights)                # 桥墩高度 (m)
    D     = pier['diameter']                            # 桥墩直径 (m)
    long_dia = pier["longitudinal_bars"]["diameter"]    # 纵向钢筋直径 (m)
    fy = steel["fy"]                                    # 钢筋屈服强度 (Pa)
    Es = steel["Es"]                                    # 钢筋弹性模量 (Pa)
    phi_ds_input = pier['curvature_ds']                 # 输入的各破坏状态曲率 (1/m)
    
    # --- 2. 计算屈服状态参数 ---
    ey    = fy / Es                                     # 钢筋屈服应变 (无量纲)
    phiy  = 2.213 * ey / D                              # 截面屈服曲率 (1/m)
    d_y   = phiy * H**2 / 3                             # 屈服位移 (m)，对应正常使用性能水平上限
    phi_ds = np.maximum(phi_ds_input, phiy).tolist()    # 确保破坏状态曲率不小于屈服曲率
    
    # --- 3. 计算塑性铰长度 ---
    # 根据《城市桥梁抗震设计规范》(CJJ 166-2011) 公式 7.3.5-2(p44)
    lp = 0.08 * H + 0.022 * fy * long_dia               # 塑性铰长度 (m)
    lp_min = 0.044 * fy * long_dia
    assert lp >= lp_min, f"塑性铰长度不符合规范: lp ({lp:.4f}) < lp_min ({lp_min:.4f})"
    
    # --- 4. 计算各性能水平下的位移限值 ---
    # 塑性转角 theta_p = (phi_u - phi_y) * lp
    # 塑性位移 d_p = theta_p * (H - lp/2)
    # 总位移 d = d_y + d_p
    d_limits = []
    k_ds = 2.0  # 延性安全系数
    for phi in phi_ds:
        # 塑性转角
        theta_p = (phi - phiy) * lp / k_ds
        # 计算总位移
        d_limit = d_y + theta_p * (H - lp / 2.0)
        d_limits.append(d_limit)

    # --- 5. 打印结果 ---
    print("--- 桥墩顶部位移限值 ---")
    print(f"屈服曲率 φ_y: {phiy:.4f} (1/m)")
    print(f"屈服位移 d_y: {d_y * 1000:.2f} mm")
    print("-" * 20)
    
    states = ["DS1 (轻微损伤)", "DS2 (中等损伤)", "DS3 (严重损伤)"]
    for i, state in enumerate(states):
        print(f"{state}:")
        print(f"  - 截面曲率 φ_ds{i+1}: {phi_ds[i]:.4f} (1/m)")
        print(f"  - 顶部位移限值 d_ds{i+1}: {d_limits[i] * 1000:.2f} mm ({d_limits[i]:.4f} m)")
    print("-" * 20)
    
    return d_limits