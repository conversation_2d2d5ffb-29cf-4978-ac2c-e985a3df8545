#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
该脚本读取地震动加速度时程文件, 自动执行时程分析并保存桥梁响应数据
输入文件为numpy数组, shape为(n, T), n为地震动数量, T为时间维度
输出为npz文件, 包含桥墩位移、支座位移和支座剪力数据
"""

import os
import numpy as np
from typing import Dict, Tuple
from dataclasses import dataclass

# 导入桥梁模型和分析器
from params import BridgeParams
from core.simply_supported_beam_model import SimplySupportedBeamModel
from analysis.enhanced_analyzer import EnhancedBridgeAnalyzer


@dataclass
class AnalysisConfig:
    """分析配置类"""
    save_temp_files: bool = False  # 是否保存临时文件
    temp_file_dir: str = "temp_results"  # 临时文件目录
    cleanup_temp_files: bool = True  # 分析完成后是否清理临时文件

    @classmethod
    def fast_mode(cls):
        """快速模式：跳过所有文件操作"""
        return cls(
            save_temp_files=False,
            cleanup_temp_files=True
        )

    @classmethod
    def debug_mode(cls):
        """调试模式：保存临时文件"""
        return cls(
            save_temp_files=True,
            cleanup_temp_files=False
        )


class ResponseDataCollector:
    """响应数据收集器"""

    def __init__(self, config: AnalysisConfig):
        self.config = config
        self.pier_responses = {}
        self.bearing_responses = {}
        self.temp_files = []  # 记录创建的临时文件

    def collect_pier_response(self, pier_key: Tuple, time_data: np.ndarray,
                            disp_data: Dict[str, np.ndarray]) -> None:
        """收集桥墩响应数据"""
        self.pier_responses[pier_key] = {
            'time': time_data,
            'disp_x': disp_data.get('disp_x', np.array([])),
            'disp_y': disp_data.get('disp_y', np.array([])),
            'disp_z': disp_data.get('disp_z', np.array([]))
        }

    def collect_bearing_response(self, bearing_idx: int, time_data: np.ndarray,
                                force_data: Dict[str, np.ndarray],
                                deform_data: Dict[str, np.ndarray],
                                coords: Dict[str, float]) -> None:
        """收集支座响应数据"""
        self.bearing_responses[bearing_idx] = {
            'x_coord': coords['x_coord'],
            'y_coord': coords['y_coord'],
            'force_data': {
                'time': time_data,
                'force_x': force_data.get('force_x', np.array([])),
                'force_y': force_data.get('force_y', np.array([])),
                'force_z': force_data.get('force_z', np.array([]))
            },
            'deform_data': {
                'time': time_data,
                'deform_x': deform_data.get('deform_x', np.array([])),
                'deform_y': deform_data.get('deform_y', np.array([])),
                'deform_z': deform_data.get('deform_z', np.array([]))
            }
        }

    def get_pier_data(self) -> Dict:
        """获取桥墩数据"""
        return self.pier_responses.copy()

    def get_bearing_data(self) -> Dict:
        """获取支座数据"""
        return self.bearing_responses.copy()

    def clear(self) -> None:
        """清空收集的数据"""
        self.pier_responses.clear()
        self.bearing_responses.clear()

    def cleanup_temp_files(self) -> None:
        """清理临时文件"""
        if self.config.cleanup_temp_files:
            for file_path in self.temp_files:
                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                except Exception as e:
                    print(f"清理临时文件 {file_path} 时出错: {e}")
            self.temp_files.clear()


def run_analysis(
    params: BridgeParams,
    ground_motion, dt=0.01, direction=1, pga=0.15,
    config: AnalysisConfig = None) -> Tuple[Dict, Dict, Dict]:
    """
    执行单个地震动的分析

    参数:
        params: 桥梁参数对象
        ground_motion: 地震记录数据
        dt: 时间步长
        direction: 地震方向 (1=纵向, 2=横向, 3=竖向)
        pga: 峰值地面加速度
        config: 分析配置对象

    返回:
        pier_data: 桥墩位移数据字典
        bearing_data: 支座响应数据字典
        analysis_stats: 分析统计信息
    """
    # 使用默认配置如果未提供
    if config is None:
        config = AnalysisConfig()

    # 创建响应数据收集器
    collector = ResponseDataCollector(config)

    # 创建桥梁模型
    model = SimplySupportedBeamModel(params)

    # 分析器
    analyzer = EnhancedBridgeAnalyzer(model)
    analyzer.set_response_collector(collector)

    analyzer.direction = direction

    # 运行分析
    analyzer.modal()
    analysis_stats = analyzer.dynamic(h=ground_motion, pga=pga, dt=dt)

    # 获取响应数据
    pier_data = collector.get_pier_data()
    bearing_data = collector.get_bearing_data()

    # 清理临时文件
    collector.cleanup_temp_files()

    return pier_data, bearing_data, analysis_stats


def process_analysis_results(pier_data: Dict, bearing_data: Dict, n_steps: int) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """
    处理分析结果，提取代表性数据并格式化为数组

    参数:
        pier_data: 桥墩位移数据
        bearing_data: 支座响应数据
        n_steps: 时间步数

    返回:
        pier_displacements: 桥墩位移数组
        bearing_displacements: 支座位移数组
        bearing_forces: 支座剪力数组
    """
    # 获取代表性桥墩数据
    pier_keys = sorted(pier_data.keys()) if pier_data else []
    n_piers = len(pier_keys)

    # 处理单跨结构（无桥墩）的情况
    if n_piers == 0:
        pier_displacements = np.zeros((0, n_steps))  # 空数组，维度正确
    else:
        pier_displacements = np.zeros((n_piers, n_steps))

        for i, pier_key in enumerate(pier_keys):
            if pier_key in pier_data:
                disp_x = pier_data[pier_key]['disp_x']
                # 确保长度匹配
                if len(disp_x) > n_steps:
                    disp_x = disp_x[:n_steps]
                elif len(disp_x) < n_steps:
                    temp = np.zeros(n_steps)
                    temp[:len(disp_x)] = disp_x
                    disp_x = temp
                pier_displacements[i, :] = disp_x

    # 获取代表性支座数据
    bearing_indices = sorted(bearing_data.keys()) if bearing_data else []
    n_bearings = len(bearing_indices)

    if n_bearings == 0:
        # 如果没有支座数据，返回空数组
        bearing_displacements = np.zeros((0, n_steps))
        bearing_forces = np.zeros((0, n_steps))
        print("警告: 没有支座数据")
    else:
        bearing_displacements = np.zeros((n_bearings, n_steps))
        bearing_forces = np.zeros((n_bearings, n_steps))

        for i, bearing_idx in enumerate(bearing_indices):
            if bearing_idx in bearing_data:
                # 支座相对位移（X方向）
                deform_x = bearing_data[bearing_idx]['deform_data']['deform_x']
                # 支座剪力（X方向）
                force_x = bearing_data[bearing_idx]['force_data']['force_x']

                # 确保长度匹配
                if len(deform_x) > n_steps:
                    deform_x = deform_x[:n_steps]
                    force_x = force_x[:n_steps]
                elif len(deform_x) < n_steps:
                    temp_deform = np.zeros(n_steps)
                    temp_force = np.zeros(n_steps)
                    temp_deform[:len(deform_x)] = deform_x
                    temp_force[:len(force_x)] = force_x
                    deform_x = temp_deform
                    force_x = temp_force

                bearing_displacements[i, :] = deform_x
                bearing_forces[i, :] = force_x

    return pier_displacements, bearing_displacements, bearing_forces


def main(
    config_file, ground_motions, gm_start, gm_end, pga, output_file
):
    """
    执行地震动时程分析，使用临时文件夹保存单条结果，最后合并为最终文件
    """
    # 分析参数设置
    dt = 0.02      # 时间步长 (s)

    # 创建分析配置
    analysis_config = AnalysisConfig.fast_mode()

    # 读取模型参数
    try:
        params = BridgeParams(config_file)
        print(f"成功读取桥梁配置: {config_file}")
    except Exception as e:
        print(f"读取桥梁配置文件时出错: {e}")
        return

    # 检查数据维度
    if ground_motions.ndim != 2:
        print(f"错误: 地震动数据维度错误: {ground_motions.shape}, 应为(n, T)")
        return

    n_motions, n_steps = ground_motions.shape
    print(f"地震动数量: {n_motions}, 时间步数: {n_steps}")

    # 创建临时文件夹
    bridge_name = os.path.basename(config_file).replace('.json', '')
    temp_dir = os.path.join(os.path.dirname(output_file), f"temp_{bridge_name}")
    print(f"临时文件夹: {temp_dir}")

    # 检查已完成的地震动
    completed_gm = set()
    if os.path.exists(temp_dir):
        for i in range(n_motions):
            temp_file = os.path.join(temp_dir, f"gm_{i:04d}.npz")
            if os.path.exists(temp_file):
                completed_gm.add(i)

    print(f"已完成地震动数量: {len(completed_gm)} / {n_motions}")

    # 检查是否所有地震动都已完成
    if len(completed_gm) == n_motions:
        print("所有地震动均已计算，直接合并结果")
        merge_gm_results(temp_dir, output_file, n_motions)
        print_analysis_summary(output_file)
        return

    # 本次需要计算的地震动
    trg_gm = list(np.arange(gm_start, gm_end))
    remaining_gm = [i for i in range(n_motions) if (i not in completed_gm) and (i in trg_gm)]
    print(f"需计算{len(remaining_gm)}条地震动: {remaining_gm}")

    # 对每个地震动执行分析
    for i in remaining_gm:
        print(f"\n处理地震动 {i}...")

        # 获取当前地震动
        ground_motion = ground_motions[i, :]

        try:
            # 执行分析
            pier_data, bearing_data, _ = run_analysis(
                params, ground_motion, pga=pga, dt=dt, config=analysis_config
            )

            # 处理分析结果
            pier_disps, bearing_disps, bearing_forces = process_analysis_results(
                pier_data, bearing_data, n_steps
            )

            # 保存单条地震动结果到临时文件
            save_single_gm_result(
                temp_dir, i, pier_disps, bearing_disps, bearing_forces,
                pier_data, bearing_data
            )

            print(f"地震动 {i} 处理完成")

        except Exception as e:
            print(f"处理地震动 {i} 时出错: {e}")
            import traceback
            traceback.print_exc()

    # 合并所有临时结果为最终文件
    print(f"\n开始合并所有地震动结果...")
    merge_gm_results(temp_dir, output_file, n_motions)

    print(f"\n分析完成! 结果已保存至: {output_file}")
    print_analysis_summary(output_file)
    
    if gm_end - gm_start != 90:
        os.remove(output_file)


def save_single_gm_result(temp_dir: str, gm_index: int,
                        pier_disps: np.ndarray, bearing_disps: np.ndarray,
                        bearing_forces: np.ndarray, pier_data: Dict, bearing_data: Dict):
    """
    保存单条地震动的分析结果到临时文件夹

    参数:
        temp_dir: 临时文件夹路径
        gm_index: 地震动索引
        pier_disps: 桥墩位移数组 (n_piers, n_steps)
        bearing_disps: 支座位移数组 (n_bearings, n_steps)
        bearing_forces: 支座剪力数组 (n_bearings, n_steps)
        pier_data: 桥墩数据字典（用于保存元数据）
        bearing_data: 支座数据字典（用于保存元数据）
    """
    try:
        # 确保临时文件夹存在
        os.makedirs(temp_dir, exist_ok=True)

        # 单条地震动结果文件名
        temp_file = os.path.join(temp_dir, f"gm_{gm_index:04d}.npz")

        # 准备元数据（只在第一个文件中保存）
        pier_keys = list(pier_data.keys()) if pier_data else []
        bearing_indices = list(bearing_data.keys()) if bearing_data else []
        is_single_span = len(pier_keys) == 0

        # 保存单条地震动结果
        np.savez_compressed(
            temp_file,
            gm_index=gm_index,
            pier_displacements=pier_disps,
            bearing_displacements=bearing_disps,
            bearing_forces=bearing_forces,
            pier_keys=pier_keys,
            bearing_indices=bearing_indices,
            is_single_span=is_single_span
        )

        print(f"地震动 {gm_index} 结果已保存至: {temp_file}")

    except Exception as e:
        print(f"保存地震动 {gm_index} 结果时出错: {e}")
        raise


def merge_gm_results(temp_dir: str, output_file: str, total_gm_count: int):
    """
    合并临时文件夹中的所有地震动结果为最终文件

    参数:
        temp_dir: 临时文件夹路径
        output_file: 最终输出文件路径
        total_gm_count: 总地震动数量
    """
    try:
        print(f"\n开始合并地震动结果...")
        print(f"临时文件夹: {temp_dir}")
        print(f"目标文件: {output_file}")

        # 检查临时文件夹是否存在
        if not os.path.exists(temp_dir):
            raise FileNotFoundError(f"临时文件夹不存在: {temp_dir}")

        # 获取所有临时文件
        temp_files = []
        for i in range(total_gm_count):
            temp_file = os.path.join(temp_dir, f"gm_{i:04d}.npz")
            if os.path.exists(temp_file):
                temp_files.append((i, temp_file))

        if not temp_files:
            raise FileNotFoundError(f"临时文件夹中没有找到任何结果文件: {temp_dir}")

        print(f"找到 {len(temp_files)} 个临时结果文件")

        # 读取第一个文件获取数据结构信息
        _, first_file = temp_files[0]
        first_data = np.load(first_file)

        # 获取数据维度
        pier_shape = first_data['pier_displacements'].shape  # (n_piers, n_steps)
        bearing_shape = first_data['bearing_displacements'].shape  # (n_bearings, n_steps)

        n_piers, n_steps = pier_shape
        n_bearings = bearing_shape[0]

        # 获取元数据
        pier_keys = first_data['pier_keys'].tolist()
        bearing_indices = first_data['bearing_indices'].tolist()
        is_single_span = first_data['is_single_span'].item()

        print(f"数据维度: 桥墩={n_piers}, 支座={n_bearings}, 时间步={n_steps}")

        # 初始化合并后的数组
        pier_displacements_all = np.zeros((n_piers, total_gm_count, n_steps))
        bearing_displacements_all = np.zeros((n_bearings, total_gm_count, n_steps))
        bearing_forces_all = np.zeros((n_bearings, total_gm_count, n_steps))

        # 合并所有文件
        merged_count = 0
        for gm_idx, temp_file in temp_files:
            try:
                data = np.load(temp_file)

                # 验证数据维度一致性
                if data['pier_displacements'].shape != pier_shape:
                    print(f"警告: 地震动 {gm_idx} 桥墩数据维度不一致，跳过")
                    continue
                if data['bearing_displacements'].shape != bearing_shape:
                    print(f"警告: 地震动 {gm_idx} 支座数据维度不一致，跳过")
                    continue

                # 复制数据
                pier_displacements_all[:, gm_idx, :] = data['pier_displacements']
                bearing_displacements_all[:, gm_idx, :] = data['bearing_displacements']
                bearing_forces_all[:, gm_idx, :] = data['bearing_forces']

                merged_count += 1

            except Exception as e:
                print(f"读取临时文件 {temp_file} 时出错: {e}")
                continue

        print(f"成功合并 {merged_count} 个地震动结果")

        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 保存合并后的结果
        np.savez_compressed(
            output_file,
            pier_displacements=pier_displacements_all,
            bearing_displacements=bearing_displacements_all,
            bearing_forces=bearing_forces_all,
            pier_keys=pier_keys,
            bearing_indices=bearing_indices,
            is_single_span=is_single_span
        )

        print(f"合并完成! 最终结果已保存至: {output_file}")

    except Exception as e:
        print(f"合并地震动结果时出错: {e}")
        raise


def save_results(output_file: str, pier_displacements: np.ndarray,
                bearing_displacements: np.ndarray, bearing_forces: np.ndarray,
                pier_data: Dict, bearing_data: Dict):
    """
    保存分析结果为npz格式 (保留原有接口用于兼容性)

    参数:
        output_file: 输出文件路径
        pier_displacements: 桥墩位移数组
        bearing_displacements: 支座位移数组
        bearing_forces: 支座剪力数组
        pier_data: 桥墩数据字典（用于保存元数据）
        bearing_data: 支座数据字典（用于保存元数据）
    """
    try:
        # 准备元数据
        pier_keys = list(pier_data.keys()) if pier_data else []
        bearing_indices = list(bearing_data.keys()) if bearing_data else []

        # 添加结构类型标识
        is_single_span = len(pier_keys) == 0

        # 保存为npz格式
        np.savez_compressed(
            output_file,
            pier_displacements=pier_displacements,
            bearing_displacements=bearing_displacements,
            bearing_forces=bearing_forces,
            pier_keys=pier_keys,
            bearing_indices=bearing_indices,
            is_single_span=is_single_span  # 添加结构类型标识
        )
    except Exception as e:
        print(f"保存结果文件时出错: {e}")
        raise


def print_analysis_summary(output_file: str):
    """
    打印分析结果摘要

    参数:
        output_file: 结果文件路径
    """
    try:
        if not os.path.exists(output_file):
            print("结果文件不存在，无法生成摘要")
            return

        data = np.load(output_file)

        print("\n=== 分析结果摘要 ===")

        # 检查是否为单跨结构
        is_single_span = data.get('is_single_span', False)
        if is_single_span:
            print("结构类型: 单跨结构（无桥墩）")
        else:
            print("结构类型: 多跨结构（含桥墩）")

        if 'pier_displacements' in data:
            pier_disps = data['pier_displacements']
            n_piers, n_motions, n_steps = pier_disps.shape

            # 对于单跨结构，实际桥墩数量为0
            actual_n_piers = 0 if is_single_span else n_piers
            print(f"桥墩数量: {actual_n_piers}")
            print(f"地震动数量: {n_motions}")
            print(f"时间步数: {n_steps}")

            # 只有在有桥墩时才计算最大位移
            if not is_single_span and n_piers > 0:
                max_pier_disp = np.max(np.abs(pier_disps))
                print(f"桥墩最大位移: {max_pier_disp:.6f} m")

        if 'bearing_displacements' in data:
            bearing_disps = data['bearing_displacements']
            n_bearings = bearing_disps.shape[0]
            print(f"支座数量: {n_bearings}")

            # 计算最大支座位移
            max_bearing_disp = np.max(np.abs(bearing_disps))
            print(f"支座最大位移: {max_bearing_disp:.6f} m")

        if 'bearing_forces' in data:
            bearing_forces = data['bearing_forces']

            # 计算最大支座剪力
            max_bearing_force = np.max(np.abs(bearing_forces))
            print(f"支座最大剪力: {max_bearing_force:.2f} N")

        print("=" * 25)

    except Exception as e:
        print(f"生成分析摘要时出错: {e}")


if __name__ == "__main__":
    """
    python analyze_xuhui_multi_gm.py --bridge_idx=14 --gm_start=60 --gm_end=70

    执行地震动时程分析，包括：
    1. 自动选择代表性桥墩和支座
    2. 记录桥墩位移、支座位移和支座剪力
    3. 将结果保存为npz格式文件
    """
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='桥梁地震响应分析脚本')
    parser.add_argument('--bridge_idx', type=int, default=2, help='桥梁索引号')
    parser.add_argument('--gm_start', type=int, default=0, help='地震动起始索引')
    parser.add_argument('--gm_end', type=int, default=90, help='地震动结束索引')

    args = parser.parse_args()
    output_path = 'out/ds_artificial'
    
    # bridges
    config_file = None
    config_list = os.listdir('configs')
    for i, c in enumerate(config_list):
        if int(c.split('-')[1]) == args.bridge_idx:
            config_file = os.path.join('configs', c)
            break

    # gm
    pga = 0.20   # 峰值地面加速度 (g)
    input_file = "gm/ds_artificial/acc_artificial_tra.npy"
    ground_motions = np.load(input_file)
    
    output_file = os.path.basename(config_file).replace('.json', f'_{args.gm_start}_{args.gm_end}.npz')
    output_file = os.path.join(output_path, output_file)
    
    if config_file is None:
        print("未找到指定桥梁")
        exit(1)

    if type(config_file) is not list:
        config_file = [config_file]
    
    for config_file_i in config_file:
        print(f"\n{'='*60}")
        print(f"开始分析桥梁: {os.path.basename(config_file_i)}")
        print(f"{'='*60}\n")
        try:
            main(config_file_i, ground_motions, args.gm_start, args.gm_end, pga, output_file)
            
            print(f"\n{'*'*60}")
            print(f"完成分析: {os.path.basename(config_file_i)}")
            print(f"{'*'*60}\n")
        except KeyboardInterrupt:
            print("\n用户中断分析")
            break  # Exit the loop if user interrupts
        except Exception as e:
            print(f"\n处理 {config_file_i} 时发生错误: {e}")
            import traceback
            traceback.print_exc()