# Pier对象技术文档

## 概述

Pier对象是桥梁结构分析模型中的核心组件，用于表示桥墩结构及其在三维空间中的节点和单元组织。该对象采用分层数据结构，支持多跨桥梁中多个桥墩的建模和分析。

## 数据结构

### 基本结构
```python
model.piers = {
    'nodes': {},      # 桥墩节点字典
    'elements': []    # 桥墩单元列表
}
```

### 节点组织方式

#### 节点存储结构
```python
model.piers['nodes'] = {
    (pier_long_idx, pier_trans_idx): [node_tags],  # 复合键索引
    ...
}
```

**键值说明：**
- `pier_long_idx`: 纵桥向索引（0, 1, 2, ...）
- `pier_trans_idx`: 横桥向索引（0, 1, 2, ...）
- `node_tags`: 该桥墩从底部到顶部的节点标签列表

#### 节点空间分布
- **纵桥向位置**: 根据跨径布置确定，位于相邻跨之间
- **横桥向位置**: 根据`num_piers_transverse`和`pier_spacing_transverse`参数确定
- **竖向分布**: 从桩基底部到墩顶，按`pier_segments`参数划分

#### 坐标系统
```python
# 节点坐标 (x, y, z)
x_coord = 纵桥向位置  # 根据跨径累计计算
y_coord = 横桥向位置  # 根据横桥向间距计算  
z_coord = 竖向高度   # 从-pier_height到0
```

### 单元组织方式

#### 单元类型
- **主要单元**: `forceBeamColumn` - 非线性纤维梁柱单元
- **积分方案**: Legendre积分，5个积分点
- **截面类型**: 圆形纤维截面（`RCCircularSection`）

#### 单元连接
```python
# 单个桥墩内的单元连接
for i in range(len(pier_nodes) - 1):
    element_connects: pier_nodes[i] -> pier_nodes[i+1]
```

#### 单元属性
- **几何变换**: `PDelta`变换（标签2）
- **截面标签**: 统一使用`model.sections['Pier']`
- **材料模型**: 
  - 核心区约束混凝土
  - 保护层非约束混凝土  
  - 纵向钢筋
  - 横向箍筋

## 关键参数

### 几何参数
- `pier_heights`: 各桥墩高度列表
- `pier_segments`: 单个桥墩的单元划分数
- `num_piers_transverse`: 横桥向桥墩数量
- `pier_spacing_transverse`: 横桥向桥墩间距

### 边界条件参数
```python
pile_soil = {
    "enable_ssi": True,                 # 是否考虑土-结构相互作用
    "m_value": 5e6,                     # 水平向抗力系数比例系数 (N/m⁴)
    "group_effect_factor": 0.8,         # 桩基群桩效应系数
    "pile_diameter_pier": 0.6,          # 桥墩桩直径 (m)
    "pile_number_pier": 6,              # 桥墩桩数量
}
```

### 截面参数
```python
pier_section = {
    "type": "circ",
    "diameter": 1.6,                    # 桥墩直径 (m)
    "concrete_cover": 0.05,             # 保护层厚度 (m)
    "longitudinal_bars": {...},         # 纵向钢筋参数
    "transverse_bars": {...}            # 横向箍筋参数
}
```

## 与其他组件的关系

### 盖梁连接
- 桥墩顶部节点与盖梁节点重合或连接
- 通过`model.cap_beams['nodes']`建立关联

### 支座连接  
- 盖梁节点作为支座的下端连接点
- 支座上端连接主梁节点

### 边界条件

桥墩底部边界条件根据`pile_soil["enable_ssi"]`参数支持两种模式：

#### 固定约束模式 (`enable_ssi = False`)
- 桥墩底部节点施加完全固定约束
- 约束所有6个自由度：`fix(bot_node, 1, 1, 1, 1, 1, 1)`
- 适用于刚性地基或简化分析

#### 土-结构相互作用模式 (`enable_ssi = True`)  
- 桥墩底部节点部分约束：`fix(bot_node, 0, 1, 1, 1, 1, 1)`
- X方向（纵桥向）自由，其他5个自由度约束
- 通过zeroLength单元连接到完全固定的桩基节点
- 考虑桩-土相互作用的弹性响应
- 使用桩基材料模型模拟土体阻力

## 分析功能

### 响应记录
- **位移记录**: 墩顶节点位移时程
- **内力记录**: 墩底截面弯矩、剪力
- **截面响应**: 曲率、应变分布

### 事故检查
- **位移超限**: 检查墩顶水平位移是否超过限值
- **破坏状态**: 评估DS1/DS2/DS3破坏等级
- **时程分析**: 记录首次进入各破坏状态的时刻

### 性能评估
- **屈服位移**: 基于截面屈服曲率计算
- **极限位移**: 考虑塑性铰长度的极限状态
- **延性系数**: 极限位移与屈服位移的比值

## 使用示例

### 访问桥墩节点
```python
# 获取第1个纵桥向、第0个横桥向桥墩的节点
pier_nodes = model.piers['nodes'][(0, 0)]
bottom_node = pier_nodes[0]   # 墩底节点
top_node = pier_nodes[-1]     # 墩顶节点
```

### 遍历所有桥墩
```python
for pier_key, pier_nodes in model.piers['nodes'].items():
    pier_long_idx, pier_trans_idx = pier_key
    print(f"桥墩({pier_long_idx},{pier_trans_idx}): {len(pier_nodes)}个节点")
```

### 获取桥墩坐标
```python
import openseespy.opensees as ops

for pier_key, pier_nodes in model.piers['nodes'].items():
    bottom_node = pier_nodes[0]
    x = ops.nodeCoord(bottom_node, 1)  # 纵桥向坐标
    y = ops.nodeCoord(bottom_node, 2)  # 横桥向坐标
    z = ops.nodeCoord(bottom_node, 3)  # 竖向坐标
```

## 注意事项

1. **索引约定**: 桥墩索引从0开始，纵桥向索引对应跨间位置
2. **节点顺序**: 节点列表按从底部到顶部的顺序排列
3. **单跨处理**: 单跨桥梁无桥墩，`model.piers['nodes']`为空字典
4. **坐标系统**: 采用右手坐标系，Z轴向上为正
5. **单元方向**: 单元局部坐标系与整体坐标系保持一致
6. **边界条件**: 根据`enable_ssi`参数选择固定约束或土-结构相互作用模式
7. **桩基建模**: SSI模式下通过zeroLength单元模拟桩-土相互作用

## 相关模块

- `components/simply_supported_beam/pier.py`: 桥墩建模实现
- `sections/circular_pier.py`: 圆形桥墩截面定义
- `analysis/recorder/pier.py`: 桥墩响应记录
- `analysis/summarize/pier.py`: 桥墩分析结果汇总
- `utils/calculate_pier_params.py`: 桥墩参数计算