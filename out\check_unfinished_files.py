# wasted codes
#%%
import os
import glob
import numpy as np
import matplotlib.pyplot as plt

os.chdir('E:/Codes/opensees/xuhui_bridges')

#%%
out_dir = 'out/ds_artificial'
flist = os.listdir(out_dir)

for fn in flist:
    if not fn[-3:] == 'npz':
        continue
    d = np.load(out_dir + '/' + fn)
    pd = d['pier_displacements']
    bd = d['bearing_displacements']
    bf = d['bearing_forces']
    
    zero_motions1 = np.where(np.abs(bd).sum((0,2)) < 1e-12)[0]
    zero_motions2 = np.where(np.abs(bf).sum((0,2)) < 1e-12)[0]
    if zero_motions1.size > 0:
        assert np.abs(zero_motions1-zero_motions2).sum() == 0
    
    if not d['is_single_span']:
        zero_motions3 = np.where(np.abs(pd).sum((0,2)) < 1e-12)[0]
        if zero_motions1.size > 0:
            assert np.abs(zero_motions1-zero_motions3).sum() == 0
    
    if zero_motions1.size > 0:
        print(fn+' '*(30-len(fn)), zero_motions1)
    
    if '_' in fn:
        temp = fn.split('.')[0].split('_')
        m, n = int(temp[1]), int(temp[2])
        
        if m==0 and n==90 and zero_motions1.size == 0:
            print(fn+' '*(30-len(fn)), '\tfinished')

del d

#%%

bridge_id = 75

search_pattern = os.path.join(out_dir, f"bridge-{bridge_id}-*_*_*.npz")
matching_files = glob.glob(search_pattern)
for fn in matching_files:
    print(fn)

#%%
ds = [np.load(f) for f in [
    'out/ds_artificial/bridge-75-田林路桂菁路桥_0_30.npz',
    'out/ds_artificial/bridge-75-田林路桂菁路桥_30_60.npz',
    'out/ds_artificial/bridge-75-田林路桂菁路桥_60_90.npz',
]]
output_file = 'out/ds_artificial/bridge-75-田林路桂菁路桥.npz'
l = np.loadtxt('gm/ds_artificial/acc_artificial_tra_length.txt')

pd = np.concatenate([d['pier_displacements'] for d in ds], 1)
bd = np.concatenate([d['bearing_displacements'] for d in ds], 1)
bf = np.concatenate([d['bearing_forces'] for d in ds], 1)
assert pd.shape[1] == bd.shape[1] == bf.shape[1] == 90

zero_motions1 = np.where(np.abs(bd).sum((0,2)) < 1e-12)[0]
zero_motions2 = np.where(np.abs(bf).sum((0,2)) < 1e-12)[0]
if zero_motions1.size > 0:
    assert np.abs(zero_motions1-zero_motions2).sum() == 0

if not ds[0]['is_single_span']:
    zero_motions3 = np.where(np.abs(pd).sum((0,2)) < 1e-12)[0]
    if zero_motions1.size > 0:
        assert np.abs(zero_motions1-zero_motions3).sum() == 0

if zero_motions1.size > 0:
    t = int(l[bridge_id])
    for i in range(90) :
        if i not in zero_motions1:
            assert np.abs(bd[0, i, t-1000]) > 1e-12
    print('unfinished gm', zero_motions1)
    # np.savez_compressed(
    #     'out/ds_artificial/bridge-75-田林路桂菁路桥_0_90.npz',
    #     pier_displacements=pd,
    #     bearing_displacements=bd,
    #     bearing_forces=bf,
    #     pier_keys=ds[0]['pier_keys'],
    #     bearing_indices=ds[0]['bearing_indices'],
    #     is_single_span=ds[0]['is_single_span'],
    # )
    
else:
    t = int(l[bridge_id])
    for i in range(90):
        assert np.abs(bd[0, i, t-1000]) > 1e-12
    np.savez_compressed(
        output_file,
        pier_displacements=pd,
        bearing_displacements=bd,
        bearing_forces=bf,
        pier_keys=ds[0]['pier_keys'],
        bearing_indices=ds[0]['bearing_indices'],
        is_single_span=ds[0]['is_single_span'],
    )
    print('finished')

del ds

# %%


d0 = np.load('out/ds_artificial/bridge-75-田林路桂菁路桥_60_90.npz')
d1 = np.load('out/ds_artificial/bridge-75-田林路桂菁路桥_86_90.npz')

# for i in range(30):
#     plt.subplot(6,5,i+1)
#     plt.plot(d0['bearing_displacements'][0, 60+i], linewidth=.45)
#     plt.xticks([])
#     plt.yticks([])
    
print(np.where(np.abs(d0['pier_displacements']).sum((0,2)) < 1e-12)[0])
print(np.where(np.abs(d1['pier_displacements']).sum((0,2)) < 1e-12)[0])

plt.plot(d0['pier_displacements'][0,-4], linewidth=.95)
plt.plot(d1['pier_displacements'][0,-4], linewidth=.45)
# plt.xlim(0,1000)

pd = np.concatenate([d0['pier_displacements'][:,:-4], d1['pier_displacements']], 1)
bd = np.concatenate([d0['bearing_displacements'][:,:-4], d1['bearing_displacements']], 1)
bf = np.concatenate([d0['bearing_forces'][:,:-4], d1['bearing_forces']], 1)

np.savez_compressed(
    'out/ds_artificial/bridge-75-田林路桂菁路桥_60_90a.npz',
    pier_displacements=pd,
    bearing_displacements=bd,
    bearing_forces=bf,
    pier_keys=d0['pier_keys'],
    bearing_indices=d0['bearing_indices'],
    is_single_span=d0['is_single_span'],
)
del d0, d1


# %%
