# Deck对象技术文档

## 概述

Deck对象是简支梁桥结构分析模型中的核心组件，用于表示桥面主梁结构及其在三维空间中的节点和单元组织。该对象采用不连续跨建模方式，每个跨独立建模，实现简支梁桥的结构特性。

## 数据结构

### 基本结构
```python
model.deck = {
    'nodes': [],      # 主梁节点列表
    'elements': []    # 主梁单元列表
}

model.span_nodes = {
    span_idx: {
        'start': node_tag,    # 跨起始节点
        'end': node_tag,      # 跨结束节点
        'all': [node_tags]    # 跨内所有节点
    }
}
```

### 节点组织方式

#### 节点存储结构
```python
model.span_nodes = {
    0: {'start': 1, 'end': 5, 'all': [1, 2, 3, 4, 5]},
    1: {'start': 6, 'end': 10, 'all': [6, 7, 8, 9, 10]},
    ...
}
```

**键值说明：**
- `span_idx`: 跨号索引（0, 1, 2, ...）
- `start`: 跨起始节点标签
- `end`: 跨结束节点标签  
- `all`: 跨内从起点到终点的所有节点标签列表

#### 节点空间分布
- **纵桥向位置**: 根据`span_lengths`参数确定各跨长度
- **横桥向位置**: 统一位于y=0位置（桥梁中心线）
- **竖向位置**: 统一位于z=0位置（桥面标高）

#### 网格划分策略
```python
# 计算网格间距
spacing = model._calculate_mesh_spacing()

# 每跨节点数量
num_nodes = max(2, int(np.ceil(span_length / spacing)))

# 节点坐标生成
x_coords = np.linspace(x_start, x_end, num_nodes, endpoint=True)
```

### 单元组织方式

#### 单元类型
- **主要单元**: `elasticBeamColumn` - 弹性梁柱单元
- **截面类型**: 根据`girder`参数选择（box/hollow_slab等）
- **几何变换**: 使用`model.transf_tag_beam`变换标签

#### 单元连接
```python
# 单跨内的单元连接
for i in range(len(span_nodes) - 1):
    element_connects: span_nodes[i] -> span_nodes[i+1]
```

#### 单元属性
- **截面标签**: 统一使用`model.sections['Deck']`
- **材料模型**: 弹性材料，基于混凝土弹性模量
- **单元方向**: 沿纵桥向（X轴方向）

## 关键参数

### 几何参数
```python
span_lengths = [30.0, 30.0, 30.0]    # 各跨长度 (m)
deck_width = 13.0                    # 桥面宽度 (m)
```

### 截面参数
```python
# 箱梁截面
girder_section = {
    "box": {
        "height": 1.8,                    # 梁高 (m)
        "top_slab_thickness": 0.25,       # 顶板厚度 (m)
        "bottom_slab_thickness": 0.20,    # 底板厚度 (m)
        "web_thickness": 0.30             # 腹板厚度 (m)
    }
}

# 空心板截面
girder_section = {
    "hollow_slab": {
        "height": 0.8,                    # 板高 (m)
        "slab_width": 1.0,                # 单板宽度 (m)
        "hollow_width": 0.6,              # 空心宽度 (m)
        "hollow_height": 0.4              # 空心高度 (m)
    }
}
```

### 边界条件
```python
# 桥台处横向约束（模拟横向挡块）
ops.fix(start_node, 0, 1, 0, 0, 0, 0)  # 左桥台
ops.fix(end_node, 0, 1, 0, 0, 0, 0)    # 右桥台
```

## 与其他组件的关系

### 支座连接
- 跨端节点作为支座的上端连接点
- 通过`model.supports`列表记录支承节点
- 支座下端连接盖梁或桥台节点

### 质量分配
- 采用集中质量法分配主梁质量
- 考虑截面几何、材料密度和附加荷载
- 端部节点分配一半单元质量，中间节点分配完整单元质量

### 荷载施加
- 自重荷载按跨长比例分配
- 活载等效为质量和静力荷载
- 节点荷载基于影响长度计算

## 分析功能

### 响应记录
- **位移记录**: 跨中节点位移时程
- **内力记录**: 关键截面弯矩、剪力
- **加速度记录**: 用于地震响应分析

### 事故检查
- **位移超限**: 检查跨中挠度是否超过限值
- **应力检查**: 评估截面应力水平
- **连接失效**: 监测跨间连接状态

### 性能评估
- **自振特性**: 计算各阶模态频率和振型
- **动力放大**: 评估动力响应放大系数
- **疲劳分析**: 基于应力时程进行疲劳评估

## 使用示例

### 访问跨节点
```python
# 获取第0跨的所有节点
span_0_nodes = model.span_nodes[0]['all']
start_node = model.span_nodes[0]['start']
end_node = model.span_nodes[0]['end']

# 获取跨中节点（近似）
mid_idx = len(span_0_nodes) // 2
mid_node = span_0_nodes[mid_idx]
```

### 遍历所有跨
```python
for span_idx, span_data in model.span_nodes.items():
    span_length = model.params.span_lengths[span_idx]
    num_nodes = len(span_data['all'])
    print(f"第{span_idx}跨: 长度{span_length}m, {num_nodes}个节点")
```

### 获取节点坐标
```python
import openseespy.opensees as ops

for span_idx, span_data in model.span_nodes.items():
    for node in span_data['all']:
        x = ops.nodeCoord(node, 1)  # 纵桥向坐标
        y = ops.nodeCoord(node, 2)  # 横桥向坐标
        z = ops.nodeCoord(node, 3)  # 竖向坐标
        print(f"节点{node}: ({x:.2f}, {y:.2f}, {z:.2f})")
```

### 计算跨中位移
```python
# 获取跨中节点位移
def get_span_mid_displacement(model, span_idx):
    span_nodes = model.span_nodes[span_idx]['all']
    mid_idx = len(span_nodes) // 2
    mid_node = span_nodes[mid_idx]
    
    disp_x = ops.nodeDisp(mid_node, 1)
    disp_y = ops.nodeDisp(mid_node, 2) 
    disp_z = ops.nodeDisp(mid_node, 3)
    
    return disp_x, disp_y, disp_z
```

## 建模策略

### 网格划分
1. 根据跨长和精度要求确定单元尺寸
2. 保证每跨至少2个单元（起点和终点）
3. 单元长度尽量均匀，避免长宽比过大

### 截面选择
1. 根据桥梁类型选择合适的截面形式
2. 箱梁适用于大跨径桥梁
3. 空心板适用于中小跨径桥梁

### 边界处理
1. 跨端节点通过支座连接下部结构
2. 桥台处设置横向约束模拟挡块
3. 简支边界条件通过支座材料实现

## 注意事项

1. **跨间独立**: 各跨节点完全独立，无直接连接
2. **节点顺序**: 节点按纵桥向从小到大排列
3. **坐标系统**: 采用右手坐标系，X轴为纵桥向
4. **单元方向**: 单元局部坐标系与整体坐标系一致
5. **网格质量**: 避免单元长宽比过大影响计算精度
6. **边界条件**: 通过支座和约束实现边界条件，不直接固定主梁节点

## 相关模块

- `components/simply_supported_beam/deck.py`: 主梁建模实现
- `sections/`: 截面定义模块
- `analysis/recorder/deck.py`: 主梁响应记录
- `analysis/summarize/deck.py`: 主梁分析结果汇总
- `utils/mesh.py`: 网格划分工具