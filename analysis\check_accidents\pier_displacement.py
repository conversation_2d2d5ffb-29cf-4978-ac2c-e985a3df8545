"""
桥墩位移超限检查模块

该模块用于检查桥梁在地震作用下是否发生桥墩位移超限事故
根据规范CJJ 166-2011城市桥梁抗震设计规范, 当桥墩顶部相对位移超过限值时,
认为发生桥墩位移超限事故
"""

import os
import numpy as np


def check_pier_displacement_accident(pier_relative_disps, model=None, params=None):
    """检查是否发生桥墩位移超限事故

    Args:
        pier_relative_disps: Dictionary of pier relative displacement data
        model: Bridge model object
        params: Bridge parameters object

    Returns:
        bool: True if pier displacement accident occurred, False otherwise
    """
    # 检查模型和参数
    if model is None and params is None:
        print("错误: 必须提供模型对象或参数对象")
        return False

    # 获取桥墩位移限值
    if params is None:
        params = model.params

    # 获取桥墩位移限值（DS1/DS2/DS3）
    pier_disp_limits = params.accidents.get("pier_top_displacement")
    if not pier_disp_limits or not isinstance(pier_disp_limits, (list, tuple)) or len(pier_disp_limits) < 3:
        print("警告: 未找到桥墩位移限值，无法检查桥墩位移状态")
        return False

    # 逐时刻检查桥墩位移，记录各桥墩首次进入各破坏状态(DS1/2/3)的时刻
    per_pier = {}  # key: (long_idx, trans_idx)

    for time, time_data in sorted(pier_relative_disps.items()):
        for pier in time_data:
            pier_key = pier['pier_key']
            pier_long_idx = pier_key[0]
            pier_trans_idx = pier_key[1]
            x_coord = pier['x_coord']
            y_coord = pier['y_coord']
            key = (pier_long_idx, pier_trans_idx)

            if key not in per_pier:
                per_pier[key] = {
                    'pier_long_idx': pier_long_idx,
                    'pier_trans_idx': pier_trans_idx,
                    'x_coord': x_coord,
                    'y_coord': y_coord,
                    'enter_times': {1: None, 2: None, 3: None},
                    'max_level': 0,
                }

            # 获取相对位移与水平合力
            rel_disp_x = pier['rel_disp_x']
            rel_disp_y = pier['rel_disp_y']
            horizontal_disp = np.sqrt(rel_disp_x**2 + rel_disp_y**2)

            # 判断达到的破坏状态等级（0~3）
            level_reached = 0
            if horizontal_disp >= pier_disp_limits[0]:
                level_reached = 1
            if horizontal_disp >= pier_disp_limits[1]:
                level_reached = 2
            if horizontal_disp >= pier_disp_limits[2]:
                level_reached = 3

            # 记录首次进入各等级的时刻
            if level_reached > 0:
                for lv in range(1, level_reached + 1):
                    if per_pier[key]['enter_times'][lv] is None:
                        per_pier[key]['enter_times'][lv] = time

            # 更新该桥墩的最大破坏等级
            if level_reached > per_pier[key]['max_level']:
                per_pier[key]['max_level'] = level_reached

    # 汇总：查找最严重破坏的桥墩（按最大等级，其次按最早进入该等级的时间）
    worst = None
    for info in per_pier.values():
        lvl = info['max_level']
        t_lvl = info['enter_times'].get(lvl) if lvl > 0 else None
        if worst is None:
            worst = info
        else:
            worst_lvl = worst['max_level']
            worst_t = worst['enter_times'].get(worst_lvl) if worst_lvl > 0 else None
            if lvl > worst_lvl or (lvl == worst_lvl and (t_lvl or float('inf')) < (worst_t or float('inf'))):
                worst = info

    # 输出结果
    if worst and worst['max_level'] > 0:
        print("---------------------")
        print("发生桥墩位移超限事故!")
        print(f"最严重破坏桥墩: ({worst['pier_long_idx']},{worst['pier_trans_idx']})")
        print(f"桥墩坐标: ({worst['x_coord']:.3f}, {worst['y_coord']:.3f})")
        print(f"最大破坏状态: DS{worst['max_level']}")
        # 输出进入不同破坏状态的时刻
        for lv in (1, 2, 3):
            t = worst['enter_times'][lv]
            if t is not None:
                print(f"进入DS{lv}的时刻: {t:.3f}s")
        return True
    else:
        print("---------------------")
        print("未发生桥墩位移超限事故")
        return False