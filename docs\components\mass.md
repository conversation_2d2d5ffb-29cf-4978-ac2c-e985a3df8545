# Mass对象技术文档

## 概述

Mass对象负责桥梁结构分析模型中质量分布的定义和施加，采用集中质量法将结构质量分配到各节点上。该对象支持主梁、桥墩、盖梁、桥台和支座等各组件的质量计算和分配，为动力分析提供准确的惯性特性。

## 数据结构

### 基本结构
```python
model.mass_data = {
    'deck': {},         # 主梁质量数据
    'piers': {},        # 桥墩质量数据  
    'cap_beams': {},    # 盖梁质量数据
    'abutments': {},    # 桥台质量数据
    'bearings': {}      # 支座质量数据
}

model.mass_distribution_method = "lumped"  # 质量分布方法
```

### 质量数据组织

#### 主梁质量数据
```python
model.mass_data['deck'] = {
    span_idx: {
        'line_mass': float,      # 线质量 (kg/m)
        'total_mass': float      # 总质量 (kg)
    }
}
```

#### 桥墩质量数据
```python
model.mass_data['piers'] = {
    (pier_long_idx, pier_trans_idx): {
        'line_mass': float,      # 线质量 (kg/m)
        'total_mass': float      # 总质量 (kg)
    }
}
```

#### 盖梁质量数据
```python
model.mass_data['cap_beams'] = {
    cap_idx: {
        'total_mass': float      # 总质量 (kg)
    }
}
```

## 关键参数

### 材料密度参数
```python
concrete_density = 2500     # 混凝土密度 (kg/m³)
steel_density = 7850        # 钢材密度 (kg/m³)
gravity = 9.81              # 重力加速度 (m/s²)
```

### 质量系数
```python
mass_factor = 1.2           # 附加质量系数（铺装、护栏等）
```

### 活载等效质量
```python
# 车辆活载等效质量
vehicle_load = 10.5e3 / 9.81  # kg/m (10.5 kN/m²)

# 人群活载等效质量  
def pedestrian_load(span_length):
    l = span_length
    if l >= 20:
        w = (4.5-2*(l-20)/80)*((20-4)/20)  # kN/m²
    else:
        w = 4.5*((20-4)/20)  # kN/m²
    return w*4*1e3 / 9.81  # kg/m
```

## 质量计算方法

### 主梁质量计算

#### 箱梁截面质量
```python
def calculate_box_girder_mass(section_params, deck_width):
    height = section_params['height']
    top_thick = section_params['top_slab_thickness']
    bottom_thick = section_params['bottom_slab_thickness']
    web_thick = section_params['web_thickness']
    
    # 计算净截面积
    hollow_width = deck_width - 2 * web_thick
    hollow_height = height - top_thick - bottom_thick
    hollow_area = hollow_width * hollow_height
    
    gross_area = deck_width * height
    net_area = gross_area - hollow_area
    
    return net_area * concrete_density
```

#### 空心板截面质量
```python
def calculate_hollow_slab_mass(section_params, deck_width):
    slab_width = section_params['slab_width']
    slab_height = section_params['height']
    hollow_width = section_params['hollow_width']
    hollow_height = section_params['hollow_height']
    
    # 单板净面积
    single_slab_area = slab_width * slab_height - hollow_width * hollow_height
    
    # 板数量
    num_slabs = int(deck_width // slab_width)
    
    # 总截面积
    total_area = single_slab_area * num_slabs
    
    return total_area * concrete_density
```

### 桥墩质量计算
```python
def calculate_pier_mass(pier_params):
    diameter = pier_params['diameter']
    area = np.pi * (diameter/2)**2
    line_mass = area * concrete_density
    return line_mass
```

### 盖梁质量计算
```python
def calculate_cap_beam_mass(cap_params, length):
    width = cap_params['width']
    height = cap_params['height']
    volume = width * height * length
    return volume * concrete_density
```

## 质量分配策略

### 集中质量法原理
- 将连续分布质量集中到节点上
- 每个节点承担相邻单元的质量贡献
- 端部节点承担一半单元质量，中间节点承担完整单元质量

### 节点质量分配

#### 主梁节点质量分配
```python
def distribute_deck_mass(span_nodes, line_mass, element_length):
    for i, node in enumerate(span_nodes):
        if i == 0 or i == len(span_nodes)-1:
            # 端部节点
            tributary_length = element_length / 2
        else:
            # 中间节点
            tributary_length = element_length
            
        nodal_mass = line_mass * tributary_length
        
        # 三个平动方向施加相等质量
        ops.mass(node, nodal_mass, nodal_mass, nodal_mass)
```

#### 桥墩节点质量分配
```python
def distribute_pier_mass(pier_nodes, line_mass, pier_height):
    num_elements = len(pier_nodes) - 1
    element_length = pier_height / num_elements
    
    for i, node in enumerate(pier_nodes):
        if i == 0 or i == len(pier_nodes)-1:
            tributary_length = element_length / 2
        else:
            tributary_length = element_length
            
        nodal_mass = line_mass * tributary_length
        ops.mass(node, nodal_mass, nodal_mass, nodal_mass)
```

## 与其他组件的关系

### 主梁质量
- 基于截面几何和材料密度计算
- 考虑附加质量（铺装、护栏等）
- 包含等效活载质量

### 桥墩质量
- 基于圆形截面和桥墩高度计算
- 分配到桥墩各节点上
- 影响结构整体动力特性

### 盖梁质量
- 基于矩形截面和盖梁长度计算
- 集中分配到盖梁节点
- 影响桥墩顶部的惯性特性

### 支座质量
- 基于支座几何和材料密度计算
- 分配到支座连接节点
- 质量相对较小但影响局部振动

## 分析功能

### 质量验证
```python
def verify_total_mass(model):
    """验证结构总质量"""
    total_mass = 0
    
    # 主梁质量
    for span_data in model.mass_data['deck'].values():
        total_mass += span_data['total_mass']
    
    # 桥墩质量
    for pier_data in model.mass_data['piers'].values():
        total_mass += pier_data['total_mass']
        
    # 盖梁质量
    for cap_data in model.mass_data['cap_beams'].values():
        total_mass += cap_data['total_mass']
        
    return total_mass
```

### 质心计算
```python
def calculate_mass_center(model):
    """计算结构质心"""
    total_mass = 0
    weighted_x = 0
    weighted_z = 0
    
    # 遍历所有节点质量
    for node in model.deck['nodes']:
        mass = ops.nodeMass(node, 1)  # 获取节点质量
        x = ops.nodeCoord(node, 1)
        z = ops.nodeCoord(node, 3)
        
        total_mass += mass
        weighted_x += mass * x
        weighted_z += mass * z
    
    center_x = weighted_x / total_mass
    center_z = weighted_z / total_mass
    
    return center_x, center_z
```

### 模态分析准备
- 质量矩阵用于特征值分析
- 影响结构自振频率和振型
- 为动力分析提供基础

## 使用示例

### 查看质量分布
```python
# 查看主梁质量分布
for span_idx, mass_data in model.mass_data['deck'].items():
    print(f"第{span_idx}跨:")
    print(f"  线质量: {mass_data['line_mass']:.2f} kg/m")
    print(f"  总质量: {mass_data['total_mass']/1000:.2f} t")
```

### 获取节点质量
```python
# 获取节点质量
def get_node_mass(node_tag):
    mass_x = ops.nodeMass(node_tag, 1)
    mass_y = ops.nodeMass(node_tag, 2)
    mass_z = ops.nodeMass(node_tag, 3)
    return mass_x, mass_y, mass_z
```

### 计算质量参与系数
```python
def calculate_mass_participation(model, mode_shapes):
    """计算模态质量参与系数"""
    # 需要结合模态分析结果
    # 计算各阶模态的质量参与系数
    pass
```

## 建模策略

### 质量建模原则
1. **完整性**: 包含所有结构组件质量
2. **准确性**: 基于实际几何和材料参数
3. **一致性**: 质量分配与单元划分一致

### 附加质量考虑
1. **铺装层**: 沥青混凝土铺装质量
2. **护栏**: 防撞护栏和栏杆质量
3. **管线**: 桥面管线和设施质量
4. **活载**: 等效车辆和人群荷载

### 质量分配优化
1. **网格相关性**: 质量分配与网格划分匹配
2. **数值稳定性**: 避免质量分配不均匀
3. **计算效率**: 合理的质量集中程度

## 注意事项

1. **质量守恒**: 确保总质量等于实际结构质量
2. **分配合理性**: 节点质量分配应符合物理规律
3. **转动惯量**: OpenSees不支持转动质量，仅考虑平动质量
4. **活载处理**: 活载等效质量应根据规范确定
5. **质量验证**: 通过总质量和质心验证质量分配正确性
6. **动力特性**: 质量分配直接影响结构动力特性

## 相关模块

- `components/simply_supported_beam/mass.py`: 质量分配实现
- `utils/mass_calculation.py`: 质量计算工具
- `analysis/modal.py`: 模态分析模块
- `analysis/recorder/mass.py`: 质量相关记录
- `params.py`: 材料密度和几何参数定义