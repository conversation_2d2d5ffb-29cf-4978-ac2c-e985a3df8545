# Load对象技术文档

## 概述

Load对象负责桥梁结构分析模型中荷载的定义和施加，包括恒荷载（自重）、活荷载和其他外部荷载。该对象采用节点荷载的方式将分布荷载转换为等效节点力，为静力和动力分析提供荷载输入。

## 数据结构

### 基本结构
```python
# 荷载通过OpenSees荷载模式实现
ops.timeSeries("Linear", 1)     # 时间序列
ops.pattern("Plain", 1, 1)      # 荷载模式

# 荷载信息存储在参数中
model.params.super_weight       # 上部结构重量 (N)
```

### 荷载组织方式

#### 荷载模式分类
- **模式1**: 恒荷载（自重、附加恒载）
- **模式2**: 活荷载（车辆、人群）
- **模式3**: 地震荷载（通过加速度时程施加）
- **模式4**: 温度荷载（温度变化效应）

#### 节点荷载累积
```python
total_loads = {
    node_tag: (fx, fy, fz, mx, my, mz),  # 节点荷载分量
    ...
}
```

## 关键参数

### 恒荷载参数
```python
super_weight = 15000e3          # 上部结构总重量 (N)
concrete_density = 2500         # 混凝土密度 (kg/m³)
steel_density = 7850            # 钢材密度 (kg/m³)
gravity = 9.81                  # 重力加速度 (m/s²)
```

### 活荷载参数
```python
# 车辆荷载
vehicle_load = 10.5e3           # 车辆荷载 (N/m²)

# 人群荷载（跨长相关）
def pedestrian_load(span_length):
    l = span_length
    if l >= 20:
        w = (4.5-2*(l-20)/80)*((20-4)/20)  # kN/m²
    else:
        w = 4.5*((20-4)/20)  # kN/m²
    return w * 4 * 1e3  # N/m (4m人行道宽度)
```

### 附加荷载系数
```python
mass_factor = 1.2               # 附加质量系数
load_factor = 1.0               # 荷载分项系数
```

## 荷载计算方法

### 自重荷载计算

#### 主梁自重分配
```python
def distribute_superstructure_weight(model):
    """将上部结构重量按跨长比例分配"""
    total_length = sum(model.params.span_lengths)
    
    # 按跨长比例分配重量
    span_weights = {}
    for i, length in enumerate(model.params.span_lengths):
        span_weights[i] = (length / total_length) * model.params.super_weight
    
    # 分配到各跨节点
    for span_idx, span_weight in span_weights.items():
        span_nodes = model.span_nodes[span_idx]['all']
        nodal_load = span_weight / len(span_nodes)
        
        for node in span_nodes:
            ops.load(node, 0, 0, -nodal_load, 0, 0, 0)
```

#### 桥墩自重计算
```python
def calculate_pier_self_weight(model, pier_idx):
    """计算桥墩自重"""
    pier_height = model.params.pier_heights[pier_idx]
    pier_diameter = model.params.pier_section['diameter']
    
    # 计算体积和重量
    pier_area = np.pi * (pier_diameter/2)**2
    pier_volume = pier_area * pier_height
    pier_weight = pier_volume * model.params.concrete_density * model.params.gravity
    
    return pier_weight
```

#### 盖梁自重计算
```python
def calculate_cap_beam_self_weight(model):
    """计算盖梁自重"""
    cap_length = model.params.deck_width
    cap_width = model.params.cap_beam_section['width']
    cap_height = model.params.cap_beam_section['height']
    
    # 计算体积和重量
    cap_volume = cap_width * cap_height * cap_length
    cap_weight = cap_volume * model.params.concrete_density * model.params.gravity
    
    return cap_weight
```

### 活荷载计算

#### 车辆荷载等效
```python
def apply_vehicle_load(model):
    """施加等效车辆荷载"""
    vehicle_intensity = 10.5e3  # N/m²
    deck_width = model.params.deck_width
    
    for span_idx, span_data in model.span_nodes.items():
        span_length = model.params.span_lengths[span_idx]
        span_nodes = span_data['all']
        
        # 计算线荷载
        line_load = vehicle_intensity * deck_width  # N/m
        
        # 分配到节点
        num_elements = len(span_nodes) - 1
        element_length = span_length / num_elements
        
        for i, node in enumerate(span_nodes):
            if i == 0 or i == len(span_nodes)-1:
                tributary_length = element_length / 2
            else:
                tributary_length = element_length
                
            nodal_load = line_load * tributary_length
            ops.load(node, 0, 0, -nodal_load, 0, 0, 0)
```

#### 人群荷载等效
```python
def apply_pedestrian_load(model, span_idx):
    """施加等效人群荷载"""
    span_length = model.params.span_lengths[span_idx]
    
    # 计算人群荷载强度
    l = span_length
    if l >= 20:
        w = (4.5-2*(l-20)/80)*((20-4)/20)  # kN/m²
    else:
        w = 4.5*((20-4)/20)  # kN/m²
    
    # 人行道宽度4m
    line_load = w * 4 * 1e3  # N/m
    
    # 分配到节点（类似车辆荷载）
    # ...
```

## 荷载组合

### 基本组合
```python
# 恒载 + 活载
load_combination_1 = {
    'dead_load': 1.0,
    'live_load': 1.0
}

# 恒载 + 0.7活载 + 地震
load_combination_2 = {
    'dead_load': 1.0,
    'live_load': 0.7,
    'earthquake': 1.0
}
```

### 荷载分项系数
```python
# 承载能力极限状态
ultimate_factors = {
    'dead_load': 1.2,
    'live_load': 1.4,
    'earthquake': 1.3
}

# 正常使用极限状态
service_factors = {
    'dead_load': 1.0,
    'live_load': 1.0,
    'earthquake': 1.0
}
```

## 与其他组件的关系

### 质量与荷载
- 质量用于动力分析（惯性力）
- 荷载用于静力分析（外力）
- 自重既产生质量也产生荷载

### 支座传力
- 荷载通过支座传递到下部结构
- 支座反力等于上部荷载
- 考虑支座的传力特性

### 边界反力
- 荷载最终传递到边界约束
- 边界反力与施加荷载平衡
- 用于验证荷载施加正确性

## 分析功能

### 荷载验证
```python
def verify_load_balance(model):
    """验证荷载平衡"""
    # 计算总施加荷载
    total_applied_load = 0
    for node in model.deck['nodes']:
        # 获取节点荷载（OpenSees没有直接命令）
        pass
    
    # 计算边界反力
    total_reaction = 0
    for support_node in model.supports:
        reaction_z = ops.nodeReaction(support_node, 3)
        total_reaction += reaction_z
    
    # 检查平衡
    balance_error = abs(total_applied_load - total_reaction)
    return balance_error
```

### 荷载效应分析
```python
def analyze_load_effects(model):
    """分析荷载效应"""
    # 施加荷载
    apply_loads(model)
    
    # 静力分析
    ops.analyze(1)
    
    # 获取响应
    displacements = {}
    for node in model.deck['nodes']:
        disp = ops.nodeDisp(node, 3)  # 竖向位移
        displacements[node] = disp
    
    return displacements
```

### 影响线分析
```python
def influence_line_analysis(model, load_position):
    """影响线分析"""
    # 在指定位置施加单位荷载
    ops.load(load_position, 0, 0, -1000, 0, 0, 0)  # 1kN
    
    # 分析
    ops.analyze(1)
    
    # 记录各点响应
    responses = {}
    for node in model.deck['nodes']:
        responses[node] = ops.nodeDisp(node, 3)
    
    return responses
```

## 使用示例

### 施加基本荷载
```python
# 创建荷载模式
ops.timeSeries("Linear", 1)
ops.pattern("Plain", 1, 1)

# 施加荷载
apply_loads(model)

# 静力分析
ops.analyze(1)
```

### 查看荷载分布
```python
def print_load_summary(model):
    """打印荷载汇总"""
    print("荷载汇总:")
    print(f"上部结构恒载: {model.params.super_weight/1000:.2f} kN")
    
    # 桥墩荷载
    total_pier_weight = 0
    for pier_idx in range(len(model.params.pier_heights)):
        pier_weight = calculate_pier_self_weight(model, pier_idx)
        total_pier_weight += pier_weight
    print(f"桥墩恒载: {total_pier_weight/1000:.2f} kN")
```

### 荷载组合分析
```python
def load_combination_analysis(model, factors):
    """荷载组合分析"""
    # 清除现有荷载
    ops.remove('loadPattern', 1)
    
    # 创建新荷载模式
    ops.timeSeries("Linear", 1)
    ops.pattern("Plain", 1, 1)
    
    # 施加组合荷载
    apply_loads(model)
    
    # 应用分项系数
    for node in model.deck['nodes']:
        # 修改节点荷载（需要重新计算）
        pass
    
    # 分析
    ops.analyze(1)
```

## 建模策略

### 荷载建模原则
1. **完整性**: 包含所有相关荷载类型
2. **准确性**: 基于规范和实际情况
3. **简化性**: 合理简化复杂荷载分布

### 荷载转换方法
1. **分布荷载**: 转换为等效节点荷载
2. **集中荷载**: 直接施加到相应节点
3. **移动荷载**: 通过影响线分析处理

### 荷载验证方法
1. **平衡检查**: 验证力和力矩平衡
2. **合理性检查**: 验证荷载大小和分布
3. **对比验证**: 与手算或其他软件对比

## 注意事项

1. **荷载单位**: 确保荷载单位一致（N, kN等）
2. **坐标方向**: 注意荷载方向与坐标系一致
3. **荷载累积**: 多次施加荷载时注意累积效应
4. **荷载清除**: 分析前清除之前的荷载模式
5. **分项系数**: 根据规范选择合适的分项系数
6. **动力荷载**: 地震荷载通过加速度时程施加

## 相关模块

- `components/simply_supported_beam/load.py`: 荷载施加实现
- `analysis/static.py`: 静力分析模块
- `analysis/dynamic.py`: 动力分析模块
- `utils/load_calculation.py`: 荷载计算工具
- `params.py`: 荷载参数定义